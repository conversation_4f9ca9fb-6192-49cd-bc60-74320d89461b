import React, { useState, useEffect } from 'react';
import {

  Box, Heading, Text, VStack, Button, SimpleGrid, Container,
  Flex, Badge, Card, CardBody, CardHeader, Stat,
  Progress, Avatar, HStack, Grid, GridItem

} from '../components/ChakraToMui';
import {

  StatArrow, StatHelpText, StatNumber, StatLabel, AvatarGroup,
  useColorModeValue

} from '../components/ChakraToMui';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../App';
import { TypewriterEffect } from '../components/TypewriterEffect';
import { SmartLoader } from '../components/LoadingAnimation';
import * as api from '../api';

const features = [
  {
    label: '🤖 AI Chatbot',
    to: '/chatbot',
    desc: 'AI siêu thông minh với voice, file analysis, code generation',
    color: 'purple',
    priority: 1
  },
  {
    label: '📅 Sự kiện',
    to: '/event',
    desc: '<PERSON><PERSON><PERSON><PERSON> lý, đ<PERSON><PERSON> ký sự kiện với AI scheduling',
    color: 'blue',
    priority: 2
  },
  {
    label: '📝 Bài kiểm tra',
    to: '/exam',
    desc: 'Tạo, làm bài kiểm tra với AI feedback',
    color: 'green',
    priority: 2
  },
  {
    label: '👥 Nhóm học',
    to: '/group',
    desc: 'Chat nhóm real-time với AI tóm tắt',
    color: 'orange',
    priority: 2
  },
  {
    label: '📚 Tài nguyên',
    to: '/resource',
    desc: 'Thư viện tài liệu thông minh',
    color: 'teal',
    priority: 3
  },
  {
    label: '🎯 Mentor',
    to: '/mentor',
    desc: 'Kết nối mentor AI-powered',
    color: 'pink',
    priority: 3
  },
  {
    label: '📊 Tiến độ',
    to: '/progress',
    desc: 'Theo dõi học tập với AI insights',
    color: 'cyan',
    priority: 3
  },
  {
    label: '🏆 Bảng xếp hạng',
    to: '/leaderboard',
    desc: 'Gamification với AI rewards',
    color: 'yellow',
    priority: 3
  },
  {
    label: '💬 Diễn đàn',
    to: '/forum',
    desc: 'Cộng đồng với AI moderation',
    color: 'red',
    priority: 4
  },
  {
    label: '📁 File Manager',
    to: '/file-manager',
    desc: 'Quản lý file thông minh',
    color: 'gray',
    priority: 4
  }
];

export default function Home() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 1250,
    activeEvents: 15,
    completedTasks: 89,
    aiInteractions: 2340
  });
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState([]);

  const bgGradient = useColorModeValue(
    'linear(to-br, teal.50, blue.50, purple.50)',
    'linear(to-br, gray.900, teal.900, purple.900)'
  );

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setLoading(false);
      setRecentActivity([
        { type: 'event', title: 'Workshop AI mới', time: '2 giờ trước' },
        { type: 'chat', title: 'Tin nhắn từ nhóm CNTT', time: '5 giờ trước' },
        { type: 'task', title: 'Hoàn thành bài tập React', time: '1 ngày trước' }
      ]);
    }, 1500);
  }, []);

  const priorityFeatures = features.filter(f => f.priority <= 2);
  const otherFeatures = features.filter(f => f.priority > 2);

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <SmartLoader type="floating" />
      </Container>
    );
  }

  return (
    <Box bgGradient={bgGradient} minH="100vh">
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Hero Section */}
          <Box textAlign="center" py={12}>
            <TypewriterEffect
              text="🚀 FPT UniHub - AI Platform"
              speed={100}
              fontSize="4xl"
              fontWeight="bold"
              bgGradient="linear(to-r, teal.400, blue.400, purple.400)"
              bgClip="text"
              mb={4}
            />
            <TypewriterEffect
              text="Nền tảng AI siêu thông minh cho sinh viên FPT University"
              speed={50}
              fontSize="xl"
              color="gray.600"
              startDelay={2000}
              mb={6}
            />
            <HStack justify="center" spacing={4} mt={6}>
              <Button
                as={RouterLink}
                to="/chatbot"
                size="lg"
                colorScheme="purple"
                bgGradient="linear(to-r, purple.400, pink.400)"
                _hover={{ bgGradient: "linear(to-r, purple.500, pink.500)" }}
              >
                🤖 Trải nghiệm AI
              </Button>
              <Button
                as={RouterLink}
                to="/features"
                size="lg"
                colorScheme="blue"
                variant="outline"
              >
                ✨ Xem tính năng
              </Button>
              <Button
                as={RouterLink}
                to="/demo"
                size="lg"
                variant="outline"
                colorScheme="teal"
              >
                🎯 Chạy Demo
              </Button>
            </HStack>
          </Box>

          {/* Stats Section */}
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={6}>
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md">
              <StatLabel>Người dùng</StatLabel>
              <StatNumber color="teal.500">{stats.totalUsers.toLocaleString()}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                23.36%
              </StatHelpText>
            </Stat>
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md">
              <StatLabel>Sự kiện đang diễn ra</StatLabel>
              <StatNumber color="blue.500">{stats.activeEvents}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                9.05%
              </StatHelpText>
            </Stat>
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md">
              <StatLabel>Nhiệm vụ hoàn thành</StatLabel>
              <StatNumber color="green.500">{stats.completedTasks}%</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                12.36%
              </StatHelpText>
            </Stat>
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md">
              <StatLabel>Tương tác AI</StatLabel>
              <StatNumber color="purple.500">{stats.aiInteractions.toLocaleString()}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                45.14%
              </StatHelpText>
            </Stat>
          </SimpleGrid>

          {/* Priority Features */}
          <Box>
            <Heading size="lg" mb={6} textAlign="center">
              ⭐ Tính năng nổi bật
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              {priorityFeatures.map(feature => (
                <Card
                  key={feature.to}
                  bg="white"
                  boxShadow="xl"
                  borderRadius="xl"
                  overflow="hidden"
                  transition="all 0.3s"
                  _hover={{ transform: "translateY(-5px)", boxShadow: "2xl" }}
                >
                  <CardHeader pb={2}>
                    <Flex align="center" justify="space-between">
                      <Heading size="md">{feature.label}</Heading>
                      <Badge colorScheme={feature.color} variant="subtle">
                        Mới
                      </Badge>
                    </Flex>
                  </CardHeader>
                  <CardBody pt={0}>
                    <Text color="gray.600" mb={4}>{feature.desc}</Text>
                    <Button
                      as={RouterLink}
                      to={feature.to}
                      colorScheme={feature.color}
                      size="sm"
                      w="full"
                    >
                      Khám phá ngay
                    </Button>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </Box>

          {/* Other Features */}
          <Box>
            <Heading size="lg" mb={6} textAlign="center">
              🛠️ Tất cả tính năng
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              {otherFeatures.map(feature => (
                <Card
                  key={feature.to}
                  bg="white"
                  boxShadow="md"
                  borderRadius="lg"
                  transition="all 0.3s"
                  _hover={{ boxShadow: "lg" }}
                >
                  <CardBody>
                    <Flex align="center" justify="space-between" mb={2}>
                      <Text fontWeight="bold">{feature.label}</Text>
                      <Badge colorScheme={feature.color} size="sm">
                        {feature.priority === 3 ? 'Phổ biến' : 'Tiện ích'}
                      </Badge>
                    </Flex>
                    <Text fontSize="sm" color="gray.600" mb={3}>
                      {feature.desc}
                    </Text>
                    <Button
                      as={RouterLink}
                      to={feature.to}
                      size="sm"
                      variant="outline"
                      colorScheme={feature.color}
                      w="full"
                    >
                      Truy cập
                    </Button>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </Box>

          {/* AI Guide Section */}
          <Card bg="gradient-to-r from-purple.500 to-pink.500" color="white">
            <CardBody>
              <VStack spacing={4} textAlign="center">
                <Heading size="lg">🎯 Hướng dẫn sử dụng AI</Heading>
                <Text fontSize="lg">
                  Trải nghiệm AI siêu thông minh với khả năng:
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
                  <Box bg="whiteAlpha.200" p={4} borderRadius="md">
                    <Text fontWeight="bold" mb={2}>🗣️ Voice Chat</Text>
                    <Text fontSize="sm">Nói chuyện trực tiếp với AI bằng giọng nói</Text>
                  </Box>
                  <Box bg="whiteAlpha.200" p={4} borderRadius="md">
                    <Text fontWeight="bold" mb={2}>📄 File Analysis</Text>
                    <Text fontSize="sm">Phân tích file, code, tài liệu tự động</Text>
                  </Box>
                  <Box bg="whiteAlpha.200" p={4} borderRadius="md">
                    <Text fontWeight="bold" mb={2}>💻 Code Generation</Text>
                    <Text fontSize="sm">Tạo code, debug, tối ưu hóa thông minh</Text>
                  </Box>
                  <Box bg="whiteAlpha.200" p={4} borderRadius="md">
                    <Text fontWeight="bold" mb={2}>🧠 Context Aware</Text>
                    <Text fontSize="sm">Hiểu ngữ cảnh, nhớ cuộc trò chuyện</Text>
                  </Box>
                </SimpleGrid>
                <Button
                  as={RouterLink}
                  to="/chatbot"
                  size="lg"
                  bg="white"
                  color="purple.500"
                  _hover={{ bg: "gray.100" }}
                >
                  🚀 Bắt đầu ngay
                </Button>
              </VStack>
            </CardBody>
          </Card>

          {/* Recent Activity */}
          {user && (
            <Card bg="white">
              <CardHeader>
                <Heading size="md">📈 Hoạt động gần đây</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={3} align="stretch">
                  {recentActivity.map((activity, index) => (
                    <Flex key={index} align="center" p={3} bg="gray.50" borderRadius="md">
                      <Box
                        w="8px"
                        h="8px"
                        bg={activity.type === 'event' ? 'blue.500' : activity.type === 'chat' ? 'green.500' : 'orange.500'}
                        borderRadius="full"
                        mr={3}
                      />
                      <Box flex={1}>
                        <Text fontWeight="medium">{activity.title}</Text>
                        <Text fontSize="sm" color="gray.600">{activity.time}</Text>
                      </Box>
                    </Flex>
                  ))}
                </VStack>
              </CardBody>
            </Card>
          )}
        </VStack>
      </Container>
    </Box>
  );
}