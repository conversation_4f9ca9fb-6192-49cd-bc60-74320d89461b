import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Button,
  Grid,
  Container,
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Stat,
  LinearProgress,
  Avatar
} from '@mui/material';
import {
  StatArrow,
  StatHelpText,
  StatNumber,
  StatLabel,
  AvatarGroup,
  useColorModeValue
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../App';
import { TypewriterEffect } from '../components/TypewriterEffect';
import { SmartLoader } from '../components/LoadingAnimation';
import * as api from '../api';

const features = [
  {
    label: '🤖 AI Chatbot',
    to: '/chatbot',
    desc: 'AI siêu thông minh với voice, file analysis, code generation',
    color: 'purple',
    priority: 1
  },
  {
    label: '📅 Sự kiện',
    to: '/event',
    desc: '<PERSON><PERSON><PERSON><PERSON> lý, đăng ký sự kiện với AI scheduling',
    color: 'blue',
    priority: 2
  },
  {
    label: '📝 Bài kiểm tra',
    to: '/exam',
    desc: '<PERSON><PERSON><PERSON>, làm bài kiểm tra với AI feedback',
    color: 'green',
    priority: 2
  },
  {
    label: '👥 Nhóm học',
    to: '/group',
    desc: 'Chat nhóm real-time với AI tóm tắt',
    color: 'orange',
    priority: 2
  },
  {
    label: '📚 Tài nguyên',
    to: '/resource',
    desc: 'Thư viện tài liệu thông minh',
    color: 'teal',
    priority: 3
  },
  {
    label: '🎯 Mentor',
    to: '/mentor',
    desc: 'Kết nối mentor AI-powered',
    color: 'pink',
    priority: 3
  },
  {
    label: '📊 Tiến độ',
    to: '/progress',
    desc: 'Theo dõi học tập với AI insights',
    color: 'cyan',
    priority: 3
  },
  {
    label: '🏆 Bảng xếp hạng',
    to: '/leaderboard',
    desc: 'Gamification với AI rewards',
    color: 'yellow',
    priority: 3
  },
  {
    label: '💬 Diễn đàn',
    to: '/forum',
    desc: 'Cộng đồng với AI moderation',
    color: 'red',
    priority: 4
  },
  {
    label: '📁 File Manager',
    to: '/file-manager',
    desc: 'Quản lý file thông minh',
    color: 'gray',
    priority: 4
  }
];

export default function Home() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 1250,
    activeEvents: 15,
    completedTasks: 89,
    aiInteractions: 2340
  });
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState([]);

  const bgGradient = useColorModeValue(
    'linear(to-br, teal.50, blue.50, purple.50)',
    'linear(to-br, gray.900, teal.900, purple.900)'
  );

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setLoading(false);
      setRecentActivity([
        { type: 'event', title: 'Workshop AI mới', time: '2 giờ trước' },
        { type: 'chat', title: 'Tin nhắn từ nhóm CNTT', time: '5 giờ trước' },
        { type: 'task', title: 'Hoàn thành bài tập React', time: '1 ngày trước' }
      ]);
    }, 1500);
  }, []);

  const priorityFeatures = features.filter(f => f.priority <= 2);
  const otherFeatures = features.filter(f => f.priority > 2);

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <SmartLoader type="floating" />
      </Container>
    );
  }

  return (
    <Box bgGradient={bgGradient} minH="100vh">
      <Container maxW="container.xl" py={8}>
        <Stack direction="column" spacing={8} align="stretch">
          {/* Hero Section */}
          <Box textAlign="center" py={12}>
            <TypewriterEffect
              text="🚀 FPT UniHub - AI Platform"
              speed={100}
              fontSize="4xl"
              fontWeight="bold"
              bgGradient="linear(to-r, teal.400, blue.400, purple.400)"
              bgClip="text"
              sx={{ mb: 4 }}
            />
            <TypewriterEffect
              text="Nền tảng AI siêu thông minh cho sinh viên FPT University"
              speed={50}
              fontSize="xl"
              color="gray.600"
              startDelay={2000}
              sx={{ mb: 6 }}
            />
            <Stack direction="row" justify="center" spacing={4} sx={{ mt: 6 }}>
              <Button
                as={RouterLink}
                to="/chatbot"
                size="lg"
                color="primary"
                bgGradient="linear(to-r, purple.400, pink.400)"
                _hover={{ bgGradient: "linear(to-r, purple.500, pink.500)" }}
              >
                🤖 Trải nghiệm AI
              </Button>
              <Button
                as={RouterLink}
                to="/features"
                size="lg"
                color="primary"
                variant="outline"
              >
                ✨ Xem tính năng
              </Button>
              <Button
                as={RouterLink}
                to="/demo"
                size="lg"
                variant="outline"
                color="primary"
              >
                🎯 Chạy Demo
              </Button>
            </Stack>
          </Box>

          {/* Stats Section */}
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={6}>
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md">
              <StatLabel>Người dùng</StatLabel>
              <StatNumber color="teal.500">{stats.totalUsers.toLocaleString()}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                23.36%
              </StatHelpText>
            </Stat>
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md">
              <StatLabel>Sự kiện đang diễn ra</StatLabel>
              <StatNumber color="blue.500">{stats.activeEvents}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                9.05%
              </StatHelpText>
            </Stat>
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md">
              <StatLabel>Nhiệm vụ hoàn thành</StatLabel>
              <StatNumber color="green.500">{stats.completedTasks}%</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                12.36%
              </StatHelpText>
            </Stat>
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md">
              <StatLabel>Tương tác AI</StatLabel>
              <StatNumber color="purple.500">{stats.aiInteractions.toLocaleString()}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                45.14%
              </StatHelpText>
            </Stat>
          </SimpleGrid>

          {/* Priority Features */}
          <Box>
            <Typography variant="h4" component="h1" size="lg" sx={{ mb: 6 }} textAlign="center">
              ⭐ Tính năng nổi bật
            </Typography>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              {priorityFeatures.map(feature => (
                <Card
                  key={feature.to}
                  bg="white"
                  boxShadow="xl"
                  borderRadius="xl"
                  overflow="hidden"
                  transition="all 0.3s"
                  _hover={{ transform: "translateY(-5px)", boxShadow: "2xl" }}
                >
                  <CardHeader pb={2}>
                    <Box display="flex" align="center" justify="space-between">
                      <Typography variant="h4" component="h1" size="md">{feature.label}</Typography>
                      <Chip colorScheme={feature.color} variant="subtle">
                        Mới
                      </Chip>
                    </Box>
                  </CardHeader>
                  <CardContent pt={0}>
                    <Typography variant="body1" color="gray.600" sx={{ mb: 4 }}>{feature.desc}</Typography>
                    <Button
                      as={RouterLink}
                      to={feature.to}
                      colorScheme={feature.color}
                      size="sm"
                      w="full"
                    >
                      Khám phá ngay
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </SimpleGrid>
          </Box>

          {/* Other Features */}
          <Box>
            <Typography variant="h4" component="h1" size="lg" sx={{ mb: 6 }} textAlign="center">
              🛠️ Tất cả tính năng
            </Typography>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              {otherFeatures.map(feature => (
                <Card
                  key={feature.to}
                  bg="white"
                  boxShadow="md"
                  borderRadius="lg"
                  transition="all 0.3s"
                  _hover={{ boxShadow: "lg" }}
                >
                  <CardContent>
                    <Box display="flex" align="center" justify="space-between" sx={{ mb: 2 }}>
                      <Typography variant="body1" fontWeight="bold">{feature.label}</Typography>
                      <Chip colorScheme={feature.color} size="sm">
                        {feature.priority === 3 ? 'Phổ biến' : 'Tiện ích'}
                      </Chip>
                    </Box>
                    <Typography variant="body1" fontSize="sm" color="gray.600" sx={{ mb: 3 }}>
                      {feature.desc}
                    </Typography>
                    <Button
                      as={RouterLink}
                      to={feature.to}
                      size="sm"
                      variant="outline"
                      colorScheme={feature.color}
                      w="full"
                    >
                      Truy cập
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </SimpleGrid>
          </Box>

          {/* AI Guide Section */}
          <Card bg="gradient-to-r from-purple.500 to-pink.500" color="white">
            <CardContent>
              <Stack direction="column" spacing={4} textAlign="center">
                <Typography variant="h4" component="h1" size="lg">🎯 Hướng dẫn sử dụng AI</Typography>
                <Typography variant="body1" fontSize="lg">
                  Trải nghiệm AI siêu thông minh với khả năng:
                </Typography>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
                  <Box bg="whiteAlpha.200" sx={{ p: 4 }} borderRadius="md">
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>🗣️ Voice Chat</Typography>
                    <Typography variant="body1" fontSize="sm">Nói chuyện trực tiếp với AI bằng giọng nói</Typography>
                  </Box>
                  <Box bg="whiteAlpha.200" sx={{ p: 4 }} borderRadius="md">
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>📄 File Analysis</Typography>
                    <Typography variant="body1" fontSize="sm">Phân tích file, code, tài liệu tự động</Typography>
                  </Box>
                  <Box bg="whiteAlpha.200" sx={{ p: 4 }} borderRadius="md">
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>💻 Code Generation</Typography>
                    <Typography variant="body1" fontSize="sm">Tạo code, debug, tối ưu hóa thông minh</Typography>
                  </Box>
                  <Box bg="whiteAlpha.200" sx={{ p: 4 }} borderRadius="md">
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>🧠 Context Aware</Typography>
                    <Typography variant="body1" fontSize="sm">Hiểu ngữ cảnh, nhớ cuộc trò chuyện</Typography>
                  </Box>
                </SimpleGrid>
                <Button
                  as={RouterLink}
                  to="/chatbot"
                  size="lg"
                  bg="white"
                  color="purple.500"
                  _hover={{ bg: "gray.100" }}
                >
                  🚀 Bắt đầu ngay
                </Button>
              </Stack>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          {user && (
            <Card bg="white">
              <CardHeader>
                <Typography variant="h4" component="h1" size="md">📈 Hoạt động gần đây</Typography>
              </CardHeader>
              <CardContent>
                <Stack direction="column" spacing={3} align="stretch">
                  {recentActivity.map((activity, index) => (
                    <Box display="flex" key={index} align="center" sx={{ p: 3 }} bg="gray.50" borderRadius="md">
                      <Box
                        w="8px"
                        h="8px"
                        bg={activity.type === 'event' ? 'blue.500' : activity.type === 'chat' ? 'green.500' : 'orange.500'}
                        borderRadius="full"
                        mr={3}
                      />
                      <Box flex={1}>
                        <Typography variant="body1" fontWeight="medium">{activity.title}</Typography>
                        <Typography variant="body1" fontSize="sm" color="gray.600">{activity.time}</Typography>
                      </Box>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          )}
        </Stack>
      </Container>
    </Box>
  );
}