import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Simple in-memory cache
class APICache {
  constructor(ttl = 5 * 60 * 1000) { // 5 minutes default TTL
    this.cache = new Map();
    this.ttl = ttl;
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  set(key, data, customTTL = null) {
    const expiry = Date.now() + (customTTL || this.ttl);
    this.cache.set(key, { data, expiry });
  }

  delete(key) {
    this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }

  // Clean expired entries
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

const cache = new APICache();

// Cleanup expired cache entries every 5 minutes
setInterval(() => cache.cleanup(), 5 * 60 * 1000);

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 second timeout
});

// Enhanced request interceptor with caching
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) config.headers.Authorization = `Bearer ${token}`;

  // Add cache key for GET requests
  if (config.method === 'get') {
    const cacheKey = `${config.url}?${JSON.stringify(config.params || {})}`;
    config.cacheKey = cacheKey;

    // Check cache for GET requests
    const cachedData = cache.get(cacheKey);
    if (cachedData && !config.skipCache) {
      // Return cached data as a resolved promise
      return Promise.reject({
        cached: true,
        data: cachedData,
        config
      });
    }
  }

  return config;
});

// Enhanced response interceptor with caching
api.interceptors.response.use(
  (response) => {
    // Cache successful GET responses
    if (response.config.method === 'get' && response.config.cacheKey) {
      cache.set(response.config.cacheKey, response.data);
    }
    return response;
  },
  (error) => {
    // Handle cached responses
    if (error.cached) {
      return Promise.resolve({
        data: error.data,
        status: 200,
        statusText: 'OK (Cached)',
        config: error.config,
        cached: true
      });
    }

    // Handle auth errors
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      cache.clear(); // Clear cache on logout
      window.location.href = '/login';
    }

    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error.message);
    }

    return Promise.reject(error);
  }
);

// Auth
export const login = (email, password) => api.post('/auth/login', { email, password });
export const register = (name, email, password) => api.post('/auth/register', { name, email, password });

// To-Do
export const getTodos = () => api.get('/todo');
export const addTodo = data => api.post('/todo', data);
export const updateTodo = (id, data) => api.put(`/todo/${id}`, data);
export const deleteTodo = id => api.delete(`/todo/${id}`);

// Group
export const getGroups = () => api.get('/group');
export const createGroup = data => api.post('/group', data);
export const addMember = (id, userId) => api.post(`/group/${id}/add`, { userId });
export const removeMember = (id, userId) => api.post(`/group/${id}/remove`, { userId });
export const createThread = (id, data) => api.post(`/group/${id}/thread`, data);

// Thread/Chat
export const getThread = id => api.get(`/thread/${id}`);
export const getMessages = id => api.get(`/thread/${id}/messages`);
export const sendMessage = (id, data) => api.post(`/thread/${id}/message`, data);
export const summarizeThread = id => api.post(`/thread/${id}/summarize`);

// Event API
export const getEvents = () => api.get('/event');
export const getEvent = (id) => api.get(`/event/${id}`);
export const createEvent = (data) => api.post('/event', data);
export const updateEvent = (id, data) => api.put(`/event/${id}`, data);
export const deleteEvent = (id) => api.delete(`/event/${id}`);
export const registerForEvent = (id) => api.post(`/event/${id}/register`);

// Exam API
export const getExams = () => api.get('/exam');
export const getExam = (id) => api.get(`/exam/${id}`);
export const createExam = (data) => api.post('/exam', data);
export const updateExam = (id, data) => api.put(`/exam/${id}`, data);
export const deleteExam = (id) => api.delete(`/exam/${id}`);
export const submitExam = (id, answers) => api.post(`/exam/${id}/submit`, { answers });

// Resource API
export const getResources = () => api.get('/resource');
export const getResource = (id) => api.get(`/resource/${id}`);
export const createResource = (data) => api.post('/resource', data);
export const updateResource = (id, data) => api.put(`/resource/${id}`, data);
export const deleteResource = (id) => api.delete(`/resource/${id}`);

// Mentor API
export const getMentors = () => api.get('/mentor');
export const getMentor = (id) => api.get(`/mentor/${id}`);
export const createMentor = (data) => api.post('/mentor', data);
export const updateMentor = (id, data) => api.put(`/mentor/${id}`, data);
export const deleteMentor = (id) => api.delete(`/mentor/${id}`);
export const bookMentorSession = (id, session) => api.post(`/mentor/${id}/book`, session);

// Feedback API
export const getFeedbacks = () => api.get('/feedback');
export const createFeedback = (data) => api.post('/feedback', data);
export const updateFeedback = (id, data) => api.put(`/feedback/${id}`, data);
export const deleteFeedback = (id) => api.delete(`/feedback/${id}`);

// Progress API
export const getProgress = () => api.get('/progress');
export const getUserProgress = (userId) => api.get(`/progress/user/${userId}`);
export const updateProgress = (data) => api.post('/progress', data);

// Notification API
export const getNotifications = () => api.get('/notification');
export const markNotificationAsRead = (id) => api.put(`/notification/${id}/read`);
export const markAllNotificationsAsRead = () => api.put('/notification/read-all');
export const deleteNotification = (id) => api.delete(`/notification/${id}`);

// Trial Course
export const getTrialCourses = () => api.get('/trial');
export const enrollInTrialCourse = (id) => api.post(`/trial/${id}/enroll`);

// Chatbot AI Gemini (legacy)
export const askGemini = prompt => api.post('/ai/chatbot', { prompt });

export const getLeaderboard = () => api.get('/leaderboard');

export const getForumPosts = () => api.get('/forum/posts');
export const createForumPost = data => api.post('/forum/post', data);
export const commentForumPost = data => api.post('/forum/comment', data);
export const voteForumPost = data => api.post('/forum/vote', data);

export const syncCalendar = data => api.post('/calendar/sync', data);

export const uploadFile = (formData) => api.post('/file/upload', formData, { headers: { 'Content-Type': 'multipart/form-data' } });
export const getFileList = () => api.get('/file/list');

export const orchestrator = async (body) => {
  const token = localStorage.getItem('token');
  const res = await axios.post(`${API_URL}/api/orchestrator/agent`, body, { headers: { Authorization: `Bearer ${token}` } });
  return res.data;
};

export const promptEnhancer = async (prompt, context) => {
  const token = localStorage.getItem('token');
  const res = await axios.post(`${API_URL}/api/prompt-enhancer`, { prompt, context }, { headers: { Authorization: `Bearer ${token}` } });
  return res.data.enhancedPrompt || res.data;
};

// Enhanced Multi-Agent AI Chatbot function
export const askAI = async (message, context = {}) => {
  const token = localStorage.getItem('token');
  const res = await axios.post(`${API_URL}/api/ai/chatbot`, {
    message,
    context,
    mode: context.mode || 'general',
    personality: context.personality || 'professional'
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

// Multi-Agent System Status
export const getSystemStatus = async () => {
  const token = localStorage.getItem('token');
  const res = await axios.get(`${API_URL}/api/ai/system/status`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

// Multi-Agent System Health Check
export const getSystemHealth = async () => {
  const token = localStorage.getItem('token');
  const res = await axios.get(`${API_URL}/api/ai/system/health`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

// Send Feedback to Multi-Agent System
export const sendFeedback = async (feedback, taskId, rating) => {
  const token = localStorage.getItem('token');
  const res = await axios.post(`${API_URL}/api/ai/feedback`, {
    feedback,
    taskId,
    rating
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

// Get System Configuration
export const getSystemConfig = async () => {
  const token = localStorage.getItem('token');
  const res = await axios.get(`${API_URL}/api/ai/system/config`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

// Task Management APIs
export const getTaskDetails = async (taskId) => {
  const token = localStorage.getItem('token');
  const res = await axios.get(`${API_URL}/api/ai/tasks/${taskId}`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

export const getActiveTasks = async () => {
  const token = localStorage.getItem('token');
  const res = await axios.get(`${API_URL}/api/ai/tasks/active`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

export const getTaskMetrics = async (taskType = null) => {
  const token = localStorage.getItem('token');
  const url = taskType
    ? `${API_URL}/api/ai/tasks/metrics?taskType=${taskType}`
    : `${API_URL}/api/ai/tasks/metrics`;
  const res = await axios.get(url, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

export const getSystemOverview = async () => {
  const token = localStorage.getItem('token');
  const res = await axios.get(`${API_URL}/api/ai/system/overview`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

export const getFeedbackSummary = async (timeRange = null) => {
  const token = localStorage.getItem('token');
  const url = timeRange
    ? `${API_URL}/api/ai/feedback/summary?timeRange=${timeRange}`
    : `${API_URL}/api/ai/feedback/summary`;
  const res = await axios.get(url, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.data;
};

export default api; 