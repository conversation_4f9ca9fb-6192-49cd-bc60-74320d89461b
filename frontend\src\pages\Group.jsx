import {
 <PERSON>ing, <PERSON>ton, <PERSON>ack, Box, Text, Input, Textarea, Badge, HStack, VStack, Avatar, AvatarGroup 
} from '../components/ChakraToMui';
import { Link } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { getGroups, createGroup } from '../api';
import { useToast } from '../components/ToastProvider';

export default function Group() {
  const [groups, setGroups] = useState([]);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const fetchGroups = async () => {
    try {
      const res = await getGroups();
      setGroups(res.data);
    } catch {
      setGroups([]);
    }
  };

  useEffect(() => { fetchGroups(); }, []);

  const handleCreate = async () => {
    if (!name.trim()) {
      toast({ title: 'Vui lòng nhập tên nhóm', status: 'warning' });
      return;
    }

    setLoading(true);
    try {
      await createGroup({ name: name.trim(), description: description.trim() });
      setName('');
      setDescription('');
      fetchGroups();
      toast({ title: 'Tạo nhóm thành công', status: 'success' });
    } catch (error) {
      console.error('Error creating group:', error);
      toast({ title: 'Tạo nhóm thất bại', status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box maxW="800px" mx="auto" p={4}>
      <Heading mb={6}>Nhóm học tập</Heading>

      {/* Create Group Form */}
      <Box p={4} borderWidth={1} borderRadius="md" mb={6} bg="orange.50">
        <VStack spacing={3}>
          <Input
            placeholder="Tên nhóm mới..."
            value={name}
            onChange={e => setName(e.target.value)}
          />
          <Textarea
            placeholder="Mô tả nhóm (tùy chọn)..."
            value={description}
            onChange={e => setDescription(e.target.value)}
            rows={2}
          />
          <Button
            colorScheme="orange"
            onClick={handleCreate}
            isLoading={loading}
            loadingText="Đang tạo..."
            w="full"
          >
            Tạo nhóm mới
          </Button>
        </VStack>
      </Box>

      {/* Groups List */}
      <Stack spacing={4}>
        {groups.length === 0 ? (
          <Box textAlign="center" py={8} color="gray.500">
            <Text fontSize="lg">Chưa có nhóm nào. Hãy tạo nhóm đầu tiên!</Text>
          </Box>
        ) : (
          groups.map(group => (
            <Box
              key={group._id}
              p={4}
              borderWidth={1}
              borderRadius="md"
              _hover={{ boxShadow: 'md', borderColor: 'orange.300' }}
              transition="all 0.2s"
              bg="white"
            >
              <VStack align="stretch" spacing={3}>
                <HStack justify="space-between" align="flex-start">
                  <VStack align="start" flex={1}>
                    <Text
                      as={Link}
                      to={`/group/${group._id}`}
                      fontSize="lg"
                      fontWeight="bold"
                      color="orange.600"
                      _hover={{ color: 'orange.800' }}
                    >
                      {group.name}
                    </Text>
                    {group.description && (
                      <Text color="gray.600" fontSize="sm">
                        {group.description}
                      </Text>
                    )}
                  </VStack>
                  <VStack align="end" spacing={2}>
                    <Badge colorScheme="orange">
                      {group.members?.length || 0} thành viên
                    </Badge>
                    {group.members && group.members.length > 0 && (
                      <AvatarGroup size="sm" max={3}>
                        {group.members.slice(0, 3).map((member, index) => (
                          <Avatar
                            key={member._id || index}
                            name={member.name || `Member ${index + 1}`}
                            src={member.avatar}
                          />
                        ))}
                      </AvatarGroup>
                    )}
                  </VStack>
                </HStack>
                <HStack>
                  <Badge colorScheme="gray" size="sm">
                    Tạo: {new Date(group.createdAt).toLocaleDateString('vi-VN')}
                  </Badge>
                  {group.threads && (
                    <Badge colorScheme="blue" size="sm">
                      {group.threads.length} cuộc trò chuyện
                    </Badge>
                  )}
                </HStack>
              </VStack>
            </Box>
          ))
        )}
      </Stack>
    </Box>
  );
}