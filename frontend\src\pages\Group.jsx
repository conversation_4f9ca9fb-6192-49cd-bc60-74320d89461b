import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  St<PERSON>,
  Box,
  TextField,
  Chip,
  Avatar,
  AvatarGroup
} from '@mui/material';
import { Link } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { getGroups, createGroup } from '../api';
import { useToast } from '../components/ToastProvider';

export default function Group() {
  const [groups, setGroups] = useState([]);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const fetchGroups = async () => {
    try {
      const res = await getGroups();
      setGroups(res.data);
    } catch {
      setGroups([]);
    }
  };

  useEffect(() => { fetchGroups(); }, []);

  const handleCreate = async () => {
    if (!name.trim()) {
      toast({ title: 'Vui lòng nhập tên nhóm', status: 'warning' });
      return;
    }

    setLoading(true);
    try {
      await createGroup({ name: name.trim(), description: description.trim() });
      setName('');
      setDescription('');
      fetchGroups();
      toast({ title: 'Tạo nhóm thành công', status: 'success' });
    } catch (error) {
      console.error('Error creating group:', error);
      toast({ title: 'Tạo nhóm thất bại', status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box maxW="800px" mx="auto" sx={{ p: 4 }}>
      <Typography variant="h4" component="h1" sx={{ mb: 6 }}>Nhóm học tập</Typography>

      {/* Create Group Form */}
      <Box sx={{ p: 4 }} borderWidth={1} borderRadius="md" sx={{ mb: 6 }} bg="orange.50">
        <Stack direction="column" spacing={3}>
          <TextField variant="outlined"
            placeholder="Tên nhóm mới..."
            value={name}
            onChange={e => setName(e.target.value)}
          />
          <Typography variant="body1"area
            placeholder="Mô tả nhóm (tùy chọn)..."
            value={description}
            onChange={e => setDescription(e.target.value)}
            rows={2}
          />
          <Button
            color="primary"
            onClick={handleCreate}
            disabled={loading}
            loadingText="Đang tạo..."
            w="full"
          >
            Tạo nhóm mới
          </Button>
        </Stack>
      </Box>

      {/* Groups List */}
      <Stack spacing={4}>
        {groups.length === 0 ? (
          <Box textAlign="center" py={8} color="gray.500">
            <Typography variant="body1" fontSize="lg">Chưa có nhóm nào. Hãy tạo nhóm đầu tiên!</Typography>
          </Box>
        ) : (
          groups.map(group => (
            <Box
              key={group._id}
              sx={{ p: 4 }}
              borderWidth={1}
              borderRadius="md"
              _hover={{ boxShadow: 'md', borderColor: 'orange.300' }}
              transition="all 0.2s"
              bg="white"
            >
              <Stack direction="column" align="stretch" spacing={3}>
                <Stack direction="row" justify="space-between" align="flex-start">
                  <Stack direction="column" align="start" flex={1}>
                    <Typography variant="body1"
                      as={Link}
                      to={`/group/${group._id}`}
                      fontSize="lg"
                      fontWeight="bold"
                      color="orange.600"
                      _hover={{ color: 'orange.800' }}
                    >
                      {group.name}
                    </Typography>
                    {group.description && (
                      <Typography variant="body1" color="gray.600" fontSize="sm">
                        {group.description}
                      </Typography>
                    )}
                  </Stack>
                  <Stack direction="column" align="end" spacing={2}>
                    <Chip color="primary">
                      {group.members?.length || 0} thành viên
                    </Chip>
                    {group.members && group.members.length > 0 && (
                      <AvatarGroup size="sm" max={3}>
                        {group.members.slice(0, 3).map((member, index) => (
                          <Avatar
                            key={member._id || index}
                            name={member.name || `Member ${index + 1}`}
                            src={member.avatar}
                          />
                        ))}
                      </AvatarGroup>
                    )}
                  </Stack>
                </Stack>
                <Stack direction="row">
                  <Chip color="primary" size="sm">
                    Tạo: {new Date(group.createdAt).toLocaleDateString('vi-VN')}
                  </Chip>
                  {group.threads && (
                    <Chip color="primary" size="sm">
                      {group.threads.length} cuộc trò chuyện
                    </Chip>
                  )}
                </Stack>
              </Stack>
            </Box>
          ))
        )}
      </Stack>
    </Box>
  );
}