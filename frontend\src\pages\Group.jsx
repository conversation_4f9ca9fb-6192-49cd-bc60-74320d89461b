import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Box,
  TextField,
  Chip,
  Avatar,
  AvatarGroup,
  Container,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Chat as ChatIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { getGroups, createGroup } from '../api';
import { useToast } from '../components/ToastProvider';

export default function Group() {
  const [groups, setGroups] = useState([]);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const fetchGroups = async () => {
    try {
      setLoading(true);
      const res = await getGroups();
      setGroups(Array.isArray(res.data) ? res.data : []);
    } catch (error) {
      console.error('Error fetching groups:', error);
      setGroups([]);
      toast({
        title: 'Lỗi',
        description: 'Không thể tải danh sách nhóm',
        status: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    if (!name.trim()) {
      toast({
        title: 'Lỗi',
        description: 'Vui lòng nhập tên nhóm',
        status: 'warning'
      });
      return;
    }

    try {
      const newGroup = {
        name: name.trim(),
        description: description.trim()
      };

      const res = await createGroup(newGroup);
      setGroups(prev => [...prev, res.data]);
      
      // Reset form
      setName('');
      setDescription('');
      
      toast({
        title: 'Thành công',
        description: 'Đã tạo nhóm mới',
        status: 'success'
      });
    } catch (error) {
      console.error('Error creating group:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể tạo nhóm',
        status: 'error'
      });
    }
  };

  useEffect(() => {
    fetchGroups();
  }, []);

  // Mock data for demonstration
  const mockGroups = [
    {
      _id: '1',
      name: 'Nhóm học React',
      description: 'Cùng nhau học và thảo luận về React.js',
      members: [
        { name: 'Nguyễn Văn A', avatar: '' },
        { name: 'Trần Thị B', avatar: '' },
        { name: 'Lê Văn C', avatar: '' }
      ],
      memberCount: 15,
      isJoined: true
    },
    {
      _id: '2',
      name: 'JavaScript Fundamentals',
      description: 'Nắm vững kiến thức cơ bản về JavaScript',
      members: [
        { name: 'Phạm Văn D', avatar: '' },
        { name: 'Hoàng Thị E', avatar: '' }
      ],
      memberCount: 23,
      isJoined: false
    },
    {
      _id: '3',
      name: 'Web Development',
      description: 'Thảo luận về phát triển web hiện đại',
      members: [
        { name: 'Vũ Văn F', avatar: '' },
        { name: 'Đặng Thị G', avatar: '' },
        { name: 'Bùi Văn H', avatar: '' },
        { name: 'Mai Thị I', avatar: '' }
      ],
      memberCount: 31,
      isJoined: true
    }
  ];

  const displayGroups = groups.length > 0 ? groups : mockGroups;

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      py: 4
    }}>
      <Container maxWidth="lg">
        <Stack spacing={4}>
          {/* Header */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              👥 Nhóm học tập
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Tham gia các nhóm học tập để cùng nhau phát triển và chia sẻ kiến thức
            </Typography>
          </Paper>

          {/* Create Group Form */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
              ➕ Tạo nhóm mới
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tên nhóm"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  variant="outlined"
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mô tả nhóm"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreate}
                  size="large"
                  sx={{ borderRadius: 2 }}
                >
                  Tạo nhóm
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Groups List */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
              📋 Danh sách nhóm ({displayGroups.length})
            </Typography>
            
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography>Đang tải...</Typography>
              </Box>
            ) : displayGroups.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <Typography variant="body1" color="text.secondary">
                  Chưa có nhóm nào. Hãy tạo nhóm đầu tiên! 🚀
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {displayGroups.map(group => (
                  <Grid item xs={12} md={6} lg={4} key={group._id}>
                    <Card 
                      elevation={2}
                      sx={{ 
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'transform 0.2s',
                        '&:hover': { transform: 'translateY(-4px)' }
                      }}
                    >
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <GroupIcon />
                          </Avatar>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" fontWeight="bold">
                              {group.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {group.memberCount} thành viên
                            </Typography>
                          </Box>
                          {group.isJoined && (
                            <Chip label="Đã tham gia" color="success" size="small" />
                          )}
                        </Stack>
                        
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {group.description || 'Không có mô tả'}
                        </Typography>
                        
                        <Divider sx={{ my: 2 }} />
                        
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Typography variant="body2" color="text.secondary">
                            Thành viên:
                          </Typography>
                          <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 24, height: 24, fontSize: '0.75rem' } }}>
                            {group.members?.map((member, index) => (
                              <Avatar key={index} sx={{ bgcolor: 'secondary.main' }}>
                                {member.name?.charAt(0) || 'U'}
                              </Avatar>
                            )) || [
                              <Avatar key={1} sx={{ bgcolor: 'secondary.main' }}>A</Avatar>,
                              <Avatar key={2} sx={{ bgcolor: 'info.main' }}>B</Avatar>,
                              <Avatar key={3} sx={{ bgcolor: 'warning.main' }}>C</Avatar>
                            ]}
                          </AvatarGroup>
                        </Stack>
                      </CardContent>
                      
                      <CardActions sx={{ p: 2, pt: 0 }}>
                        <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                          {group.isJoined ? (
                            <>
                              <Button
                                variant="contained"
                                startIcon={<ChatIcon />}
                                component={Link}
                                to={`/chat?group=${group._id}`}
                                size="small"
                                sx={{ flex: 1 }}
                              >
                                Chat
                              </Button>
                              <IconButton size="small" color="primary">
                                <SettingsIcon />
                              </IconButton>
                            </>
                          ) : (
                            <Button
                              variant="outlined"
                              startIcon={<PersonIcon />}
                              size="small"
                              sx={{ flex: 1 }}
                              onClick={() => {
                                toast({
                                  title: 'Thành công',
                                  description: `Đã tham gia nhóm ${group.name}`,
                                  status: 'success'
                                });
                              }}
                            >
                              Tham gia
                            </Button>
                          )}
                        </Stack>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Stack>
      </Container>
    </Box>
  );
}
