import { io } from 'socket.io-client';

/**
 * Enhanced Socket.IO Service with reconnection, error handling, and event management
 */
class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.eventListeners = new Map();
    this.rooms = new Set();
    this.connectionPromise = null;
    this.heartbeatInterval = null;
    this.metrics = {
      connectTime: null,
      disconnectCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errors: 0
    };
  }

  /**
   * Initialize socket connection
   */
  async connect(options = {}) {
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    const defaultOptions = {
      url: import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001',
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
      timeout: 10000,
      forceNew: false,
      ...options
    };

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        this.socket = io(defaultOptions.url, defaultOptions);
        this.setupEventHandlers(resolve, reject);
        this.socket.connect();
      } catch (error) {
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  /**
   * Setup socket event handlers
   */
  setupEventHandlers(resolve, reject) {
    this.socket.on('connect', () => {
      console.log('✅ Socket connected:', this.socket.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.metrics.connectTime = Date.now();
      this.startHeartbeat();
      
      // Rejoin rooms after reconnection
      this.rooms.forEach(room => {
        this.socket.emit('join', room);
      });
      
      resolve(this.socket);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
      this.isConnected = false;
      this.metrics.disconnectCount++;
      this.stopHeartbeat();
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, reconnect manually
        this.socket.connect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('🔌 Socket connection error:', error);
      this.metrics.errors++;
      
      if (this.reconnectAttempts === 0) {
        reject(error);
      }
      
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('❌ Max reconnection attempts reached');
        this.connectionPromise = null;
      }
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 Socket reconnected after ${attemptNumber} attempts`);
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('🔄 Socket reconnection error:', error);
      this.metrics.errors++;
    });

    this.socket.on('error', (error) => {
      console.error('⚠️ Socket error:', error);
      this.metrics.errors++;
    });

    // Handle incoming messages
    this.socket.onAny((eventName, ...args) => {
      this.metrics.messagesReceived++;
      
      // Trigger registered event listeners
      const listeners = this.eventListeners.get(eventName);
      if (listeners) {
        listeners.forEach(callback => {
          try {
            callback(...args);
          } catch (error) {
            console.error(`Error in event listener for ${eventName}:`, error);
          }
        });
      }
    });
  }

  /**
   * Start heartbeat to monitor connection
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.socket.emit('ping', Date.now());
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Disconnect socket
   */
  disconnect() {
    if (this.socket) {
      this.stopHeartbeat();
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.connectionPromise = null;
      this.rooms.clear();
      this.eventListeners.clear();
    }
  }

  /**
   * Emit event with error handling and retry
   */
  async emit(eventName, data, options = {}) {
    if (!this.isConnected) {
      await this.connect();
    }

    const { timeout = 5000, retries = 1 } = options;

    return new Promise((resolve, reject) => {
      let attempts = 0;

      const attemptEmit = () => {
        attempts++;
        this.metrics.messagesSent++;

        const timeoutId = setTimeout(() => {
          if (attempts < retries) {
            attemptEmit();
          } else {
            reject(new Error(`Emit timeout for event: ${eventName}`));
          }
        }, timeout);

        this.socket.emit(eventName, data, (response) => {
          clearTimeout(timeoutId);
          
          if (response && response.error) {
            if (attempts < retries) {
              attemptEmit();
            } else {
              reject(new Error(response.error));
            }
          } else {
            resolve(response);
          }
        });
      };

      attemptEmit();
    });
  }

  /**
   * Subscribe to events
   */
  on(eventName, callback) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, new Set());
    }
    
    this.eventListeners.get(eventName).add(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(eventName);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.eventListeners.delete(eventName);
        }
      }
    };
  }

  /**
   * Subscribe to event once
   */
  once(eventName, callback) {
    const unsubscribe = this.on(eventName, (...args) => {
      unsubscribe();
      callback(...args);
    });
    
    return unsubscribe;
  }

  /**
   * Join a room
   */
  async joinRoom(roomId) {
    if (!this.isConnected) {
      await this.connect();
    }

    return new Promise((resolve, reject) => {
      this.socket.emit('join', roomId, (response) => {
        if (response && response.success) {
          this.rooms.add(roomId);
          resolve(response);
        } else {
          reject(new Error(response?.error || 'Failed to join room'));
        }
      });
    });
  }

  /**
   * Leave a room
   */
  async leaveRoom(roomId) {
    if (!this.isConnected) {
      return;
    }

    return new Promise((resolve, reject) => {
      this.socket.emit('leave', roomId, (response) => {
        if (response && response.success) {
          this.rooms.delete(roomId);
          resolve(response);
        } else {
          reject(new Error(response?.error || 'Failed to leave room'));
        }
      });
    });
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      socketId: this.socket?.id,
      rooms: Array.from(this.rooms),
      reconnectAttempts: this.reconnectAttempts,
      metrics: this.metrics
    };
  }

  /**
   * Get socket metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      uptime: this.metrics.connectTime ? Date.now() - this.metrics.connectTime : 0,
      reconnectAttempts: this.reconnectAttempts,
      activeListeners: this.eventListeners.size,
      joinedRooms: this.rooms.size
    };
  }
}

// Singleton instance
const socketService = new SocketService();

export default socketService;
export { SocketService };
