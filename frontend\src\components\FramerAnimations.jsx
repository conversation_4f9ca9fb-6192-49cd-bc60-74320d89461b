import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Box, Paper, <PERSON><PERSON>, Card, CardContent } from '@mui/material';

// Animation variants
export const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -60 }
};

export const fadeInDown = {
  initial: { opacity: 0, y: -60 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 60 }
};

export const slideInLeft = {
  initial: { opacity: 0, x: -100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 100 }
};

export const slideInRight = {
  initial: { opacity: 0, x: 100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -100 }
};

export const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 }
};

export const rotateIn = {
  initial: { opacity: 0, rotate: -180 },
  animate: { opacity: 1, rotate: 0 },
  exit: { opacity: 0, rotate: 180 }
};

// Stagger container
export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Hover animations
export const hoverScale = {
  whileHover: { scale: 1.05 },
  whileTap: { scale: 0.95 }
};

export const hoverLift = {
  whileHover: { y: -8, boxShadow: "0 10px 25px rgba(0,0,0,0.15)" },
  whileTap: { y: 0 }
};

// Motion components
export const MotionBox = motion(Box);
export const MotionPaper = motion(Paper);
export const MotionButton = motion(Button);
export const MotionCard = motion(Card);

// Enhanced animated components
export const AnimatedContainer = ({ 
  children, 
  variant = fadeInUp, 
  duration = 0.6, 
  delay = 0,
  ...props 
}) => (
  <MotionBox
    variants={variant}
    initial="initial"
    animate="animate"
    exit="exit"
    transition={{ duration, delay, ease: "easeOut" }}
    {...props}
  >
    {children}
  </MotionBox>
);

export const StaggeredContainer = ({ children, ...props }) => (
  <MotionBox
    variants={staggerContainer}
    initial="initial"
    animate="animate"
    {...props}
  >
    {children}
  </MotionBox>
);

export const AnimatedCard = ({ 
  children, 
  hover = true, 
  elevation = 2,
  ...props 
}) => (
  <MotionCard
    variants={fadeInUp}
    initial="initial"
    animate="animate"
    {...(hover ? hoverLift : {})}
    elevation={elevation}
    transition={{ duration: 0.3 }}
    sx={{
      cursor: hover ? 'pointer' : 'default',
      ...props.sx
    }}
    {...props}
  >
    {children}
  </MotionCard>
);

export const AnimatedButton = ({ 
  children, 
  variant = 'contained',
  color = 'primary',
  ...props 
}) => (
  <MotionButton
    variant={variant}
    color={color}
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    transition={{ type: "spring", stiffness: 400, damping: 17 }}
    {...props}
  >
    {children}
  </MotionButton>
);

export const FloatingElement = ({ children, ...props }) => (
  <MotionBox
    animate={{
      y: [0, -10, 0],
    }}
    transition={{
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut"
    }}
    {...props}
  >
    {children}
  </MotionBox>
);

export const PulseElement = ({ children, ...props }) => (
  <MotionBox
    animate={{
      scale: [1, 1.05, 1],
    }}
    transition={{
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }}
    {...props}
  >
    {children}
  </MotionBox>
);

// Page transition wrapper
export const PageTransition = ({ children, ...props }) => (
  <AnimatePresence mode="wait">
    <MotionBox
      key={location.pathname}
      variants={fadeInUp}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.4, ease: "easeInOut" }}
      {...props}
    >
      {children}
    </MotionBox>
  </AnimatePresence>
);

// Reveal on scroll
export const RevealOnScroll = ({ 
  children, 
  threshold = 0.1,
  variant = fadeInUp,
  ...props 
}) => {
  return (
    <MotionBox
      variants={variant}
      initial="initial"
      whileInView="animate"
      viewport={{ once: true, amount: threshold }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      {...props}
    >
      {children}
    </MotionBox>
  );
};

// Loading spinner with animation
export const AnimatedSpinner = ({ size = 40, color = 'primary' }) => (
  <MotionBox
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    sx={{
      width: size,
      height: size,
      border: `3px solid`,
      borderColor: `${color}.light`,
      borderTopColor: `${color}.main`,
      borderRadius: '50%',
    }}
  />
);

// Typewriter effect
export const TypewriterText = ({ 
  text, 
  delay = 0, 
  speed = 0.05,
  component: Component = 'span',
  ...props 
}) => {
  return (
    <Component {...props}>
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            delay: delay + index * speed,
            duration: 0.1
          }}
        >
          {char}
        </motion.span>
      ))}
    </Component>
  );
};

// Morphing background
export const MorphingBackground = ({ children, ...props }) => (
  <MotionBox
    animate={{
      background: [
        "linear-gradient(45deg, #667eea 0%, #764ba2 100%)",
        "linear-gradient(45deg, #f093fb 0%, #f5576c 100%)",
        "linear-gradient(45deg, #4facfe 0%, #00f2fe 100%)",
        "linear-gradient(45deg, #667eea 0%, #764ba2 100%)"
      ]
    }}
    transition={{
      duration: 10,
      repeat: Infinity,
      ease: "easeInOut"
    }}
    {...props}
  >
    {children}
  </MotionBox>
);

export default {
  AnimatedContainer,
  StaggeredContainer,
  AnimatedCard,
  AnimatedButton,
  FloatingElement,
  PulseElement,
  PageTransition,
  RevealOnScroll,
  AnimatedSpinner,
  TypewriterText,
  MorphingBackground,
  fadeInUp,
  fadeInDown,
  slideInLeft,
  slideInRight,
  scaleIn,
  rotateIn,
  hoverScale,
  hoverLift
};
