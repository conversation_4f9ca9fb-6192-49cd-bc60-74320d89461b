import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Stack,
  Paper,
  TextField,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  Divider,
  <PERSON><PERSON><PERSON>,
  Badge
} from '@mui/material';
import {
  Comment as CommentIcon,
  Reply as ReplyIcon,
  MoreVert as MoreIcon,
  ThumbUp as LikeIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Resolve as ResolveIcon
} from '@mui/icons-material';

const CommentsSystem = ({ blockId, comments = [], onAddComment, onReplyComment, onResolveComment }) => {
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [replyTo, setReplyTo] = useState(null);
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedComment, setSelectedComment] = useState(null);

  const mockComments = [
    {
      id: 1,
      author: '<PERSON>',
      avatar: 'J',
      content: 'This section needs more detail about the implementation.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      likes: 2,
      resolved: false,
      replies: [
        {
          id: 2,
          author: '<PERSON>',
          avatar: 'JS',
          content: 'I agree, let me add more technical specifications.',
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
          likes: 1
        }
      ]
    },
    {
      id: 3,
      author: 'Mike Johnson',
      avatar: 'M',
      content: 'Great work on this! The structure is very clear.',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      likes: 5,
      resolved: true,
      replies: []
    }
  ];

  const displayComments = comments.length > 0 ? comments : mockComments;

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    const comment = {
      id: Date.now(),
      author: 'Current User',
      avatar: 'U',
      content: newComment,
      timestamp: new Date(),
      likes: 0,
      resolved: false,
      replies: []
    };

    onAddComment?.(blockId, comment);
    setNewComment('');
  };

  const handleReply = (commentId) => {
    if (!newComment.trim()) return;

    const reply = {
      id: Date.now(),
      author: 'Current User',
      avatar: 'U',
      content: newComment,
      timestamp: new Date(),
      likes: 0
    };

    onReplyComment?.(commentId, reply);
    setNewComment('');
    setReplyTo(null);
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const renderComment = (comment, isReply = false) => (
    <Box key={comment.id} sx={{ mb: 2, ml: isReply ? 4 : 0 }}>
      <Paper
        variant="outlined"
        sx={{
          p: 2,
          bgcolor: comment.resolved ? 'success.50' : 'background.paper',
          border: comment.resolved ? 1 : 0,
          borderColor: 'success.main'
        }}
      >
        <Stack direction="row" spacing={2}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            {comment.avatar}
          </Avatar>
          
          <Box sx={{ flexGrow: 1 }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography variant="subtitle2" fontWeight="bold">
                  {comment.author}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {formatTimeAgo(comment.timestamp)}
                </Typography>
                {comment.resolved && (
                  <Chip label="Resolved" size="small" color="success" />
                )}
              </Stack>
              
              <IconButton
                size="small"
                onClick={(e) => {
                  setSelectedComment(comment);
                  setMenuAnchor(e.currentTarget);
                }}
              >
                <MoreIcon fontSize="small" />
              </IconButton>
            </Stack>
            
            <Typography variant="body2" sx={{ mb: 1 }}>
              {comment.content}
            </Typography>
            
            <Stack direction="row" spacing={1} alignItems="center">
              <Button
                size="small"
                startIcon={<LikeIcon />}
                sx={{ minWidth: 'auto', px: 1 }}
              >
                {comment.likes}
              </Button>
              
              {!isReply && (
                <Button
                  size="small"
                  startIcon={<ReplyIcon />}
                  onClick={() => setReplyTo(comment.id)}
                >
                  Reply
                </Button>
              )}
              
              {!comment.resolved && !isReply && (
                <Button
                  size="small"
                  startIcon={<ResolveIcon />}
                  onClick={() => onResolveComment?.(comment.id)}
                >
                  Resolve
                </Button>
              )}
            </Stack>
          </Box>
        </Stack>
      </Paper>
      
      {/* Replies */}
      {comment.replies?.map(reply => renderComment(reply, true))}
      
      {/* Reply Input */}
      {replyTo === comment.id && (
        <Box sx={{ mt: 2, ml: 4 }}>
          <Stack spacing={2}>
            <TextField
              fullWidth
              multiline
              rows={2}
              placeholder="Write a reply..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              variant="outlined"
              size="small"
            />
            <Stack direction="row" spacing={1}>
              <Button
                variant="contained"
                size="small"
                onClick={() => handleReply(comment.id)}
                disabled={!newComment.trim()}
              >
                Reply
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  setReplyTo(null);
                  setNewComment('');
                }}
              >
                Cancel
              </Button>
            </Stack>
          </Stack>
        </Box>
      )}
    </Box>
  );

  return (
    <Box sx={{ position: 'relative' }}>
      {/* Comments Toggle Button */}
      <Tooltip title="Comments">
        <IconButton
          onClick={() => setShowComments(!showComments)}
          sx={{
            position: 'absolute',
            right: -40,
            top: 0,
            opacity: showComments ? 1 : 0.6,
            '&:hover': { opacity: 1 }
          }}
        >
          <Badge badgeContent={displayComments.length} color="primary">
            <CommentIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      {/* Comments Panel */}
      {showComments && (
        <Paper
          elevation={4}
          sx={{
            position: 'absolute',
            right: -350,
            top: 0,
            width: 320,
            maxHeight: 500,
            overflow: 'auto',
            p: 2,
            zIndex: 1000,
            borderRadius: 2
          }}
        >
          <Typography variant="h6" gutterBottom>
            💬 Comments ({displayComments.length})
          </Typography>
          
          <Divider sx={{ mb: 2 }} />
          
          {/* Comments List */}
          <Box sx={{ mb: 2 }}>
            {displayComments.map(comment => renderComment(comment))}
          </Box>
          
          {/* Add Comment */}
          {replyTo === null && (
            <Stack spacing={2}>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                variant="outlined"
                size="small"
              />
              <Button
                variant="contained"
                onClick={handleAddComment}
                disabled={!newComment.trim()}
                sx={{ alignSelf: 'flex-start' }}
              >
                Comment
              </Button>
            </Stack>
          )}
        </Paper>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
        {!selectedComment?.resolved && (
          <MenuItem onClick={() => {
            onResolveComment?.(selectedComment?.id);
            setMenuAnchor(null);
          }}>
            <ResolveIcon sx={{ mr: 1 }} />
            Resolve
          </MenuItem>
        )}
      </Menu>
    </Box>
  );
};

export default CommentsSystem;
