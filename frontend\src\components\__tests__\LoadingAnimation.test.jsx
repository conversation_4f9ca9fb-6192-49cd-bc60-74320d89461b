import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderWithProviders, screen, waitFor } from '../../utils/testUtils';
import LoadingAnimation from '../LoadingAnimation';

describe('LoadingAnimation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', () => {
      renderWithProviders(<LoadingAnimation />);
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('renders with custom message', () => {
      const message = 'Custom loading message';
      renderWithProviders(<LoadingAnimation message={message} />);
      expect(screen.getByText(message)).toBeInTheDocument();
    });

    it('renders with different variants', () => {
      const variants = ['circular', 'dots', 'skeleton', 'pulse'];
      
      variants.forEach(variant => {
        const { unmount } = renderWithProviders(
          <LoadingAnimation variant={variant} />
        );
        // Each variant should render without errors
        expect(screen.getByTestId(`loading-${variant}`) || screen.getByRole('progressbar')).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('Props', () => {
    it('applies custom size', () => {
      renderWithProviders(<LoadingAnimation size={60} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '60px', height: '60px' });
    });

    it('renders fullscreen when specified', () => {
      renderWithProviders(<LoadingAnimation fullScreen />);
      const container = screen.getByTestId('loading-container');
      expect(container).toHaveStyle({ minHeight: '100vh' });
    });

    it('renders as overlay when specified', () => {
      renderWithProviders(<LoadingAnimation overlay />);
      const overlay = screen.getByTestId('loading-overlay');
      expect(overlay).toHaveStyle({ 
        position: 'fixed',
        zIndex: '9999'
      });
    });
  });

  describe('Variants', () => {
    it('renders thinking variant with AI emoji', () => {
      renderWithProviders(<LoadingAnimation variant="thinking" />);
      expect(screen.getByText('🤖')).toBeInTheDocument();
      expect(screen.getByText('AI đang suy nghĩ')).toBeInTheDocument();
    });

    it('renders typing variant with dots animation', () => {
      renderWithProviders(<LoadingAnimation variant="typing" />);
      expect(screen.getByText('Đang gõ...')).toBeInTheDocument();
    });

    it('renders progress variant with percentage', () => {
      renderWithProviders(
        <LoadingAnimation variant="progress" progress={50} />
      );
      expect(screen.getByText('50%')).toBeInTheDocument();
    });

    it('renders skeleton variant', () => {
      renderWithProviders(
        <LoadingAnimation variant="skeleton" skeletonType="card" />
      );
      expect(screen.getByTestId('skeleton-loader')).toBeInTheDocument();
    });
  });

  describe('Animations', () => {
    it('applies fade in animation', async () => {
      renderWithProviders(<LoadingAnimation />);
      const container = screen.getByTestId('loading-container');
      
      await waitFor(() => {
        expect(container).toHaveStyle({ opacity: '1' });
      });
    });

    it('applies pulse animation for pulse variant', () => {
      renderWithProviders(<LoadingAnimation variant="pulse" />);
      const container = screen.getByTestId('loading-container');
      expect(container).toHaveStyle({ 
        animation: expect.stringContaining('pulse')
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<LoadingAnimation />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveAttribute('aria-label', expect.any(String));
    });

    it('announces loading state to screen readers', () => {
      const message = 'Loading content';
      renderWithProviders(<LoadingAnimation message={message} />);
      expect(screen.getByLabelText(message)).toBeInTheDocument();
    });

    it('has proper color contrast', () => {
      renderWithProviders(<LoadingAnimation />);
      const progressbar = screen.getByRole('progressbar');
      const styles = window.getComputedStyle(progressbar);
      
      // Basic contrast check (in real tests, use proper contrast calculation)
      expect(styles.color).not.toBe(styles.backgroundColor);
    });
  });

  describe('Performance', () => {
    it('renders quickly', () => {
      const start = performance.now();
      renderWithProviders(<LoadingAnimation />);
      const end = performance.now();
      
      expect(end - start).toBeLessThan(100); // Should render in less than 100ms
    });

    it('does not cause memory leaks', () => {
      const initialMemory = performance.memory?.usedJSHeapSize || 0;
      
      const { unmount } = renderWithProviders(<LoadingAnimation />);
      unmount();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be minimal after unmount
      expect(memoryIncrease).toBeLessThan(1024 * 1024); // Less than 1MB
    });
  });

  describe('Error Handling', () => {
    it('handles invalid variant gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      renderWithProviders(<LoadingAnimation variant="invalid" />);
      
      // Should fallback to default variant
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });

    it('handles negative progress values', () => {
      renderWithProviders(
        <LoadingAnimation variant="progress" progress={-10} />
      );
      
      // Should clamp to 0
      expect(screen.getByText('0%')).toBeInTheDocument();
    });

    it('handles progress values over 100', () => {
      renderWithProviders(
        <LoadingAnimation variant="progress" progress={150} />
      );
      
      // Should clamp to 100
      expect(screen.getByText('100%')).toBeInTheDocument();
    });
  });

  describe('Integration', () => {
    it('works with theme provider', () => {
      renderWithProviders(<LoadingAnimation />);
      const progressbar = screen.getByRole('progressbar');
      
      // Should inherit theme colors
      const styles = window.getComputedStyle(progressbar);
      expect(styles.color).toBeTruthy();
    });

    it('responds to theme changes', () => {
      // This would require a more complex setup with theme switching
      // For now, just ensure it renders with theme
      renderWithProviders(<LoadingAnimation />);
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('Custom Props', () => {
    it('forwards custom props to container', () => {
      renderWithProviders(
        <LoadingAnimation data-testid="custom-loading" className="custom-class" />
      );
      
      const container = screen.getByTestId('custom-loading');
      expect(container).toHaveClass('custom-class');
    });

    it('applies custom styles', () => {
      const customStyle = { backgroundColor: 'red' };
      renderWithProviders(<LoadingAnimation style={customStyle} />);
      
      const container = screen.getByTestId('loading-container');
      expect(container).toHaveStyle(customStyle);
    });
  });
});
