import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  TextField,
  Checkbox,
  Chip,
  Select
} from '@mui/material';
import { useEffect, useState } from 'react';
import { getTodos, addTodo, deleteTodo, updateTodo } from '../api';
import { useToast } from '../components/ToastProvider';

export default function ToDo() {
  const [todos, setTodos] = useState([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState('normal');
  const [deadline, setDeadline] = useState('');
  const toast = useToast();

  const fetchTodos = async () => {
    try {
      const res = await getTodos();
      setTodos(res.data);
    } catch {
      setTodos([]);
    }
  };

  useEffect(() => { fetchTodos(); }, []);

  const handleAdd = async () => {
    if (!title) return;
    try {
      const todoData = {
        title,
        description,
        type,
        deadline: deadline ? new Date(deadline) : null
      };
      await addTodo(todoData);
      setTitle('');
      setDescription('');
      setType('normal');
      setDeadline('');
      fetchTodos();
      toast({ title: 'Thêm to-do thành công', status: 'success' });
    } catch {
      toast({ title: 'Thêm to-do thất bại', status: 'error' });
    }
  };

  const handleToggle = async (id, isDone) => {
    try {
      await updateTodo(id, { isDone: !isDone });
      fetchTodos();
    } catch {
      toast({ title: 'Cập nhật thất bại', status: 'error' });
    }
  };

  const handleDelete = async id => {
    try {
      await deleteTodo(id);
      fetchTodos();
      toast({ title: 'Xóa thành công', status: 'success' });
    } catch {
      toast({ title: 'Xóa thất bại', status: 'error' });
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'exam': return 'red';
      case 'study': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <Box maxW="800px" mx="auto" sx={{ p: 4 }}>
      <Typography variant="h4" component="h1" sx={{ mb: 6 }}>To-Do List AI</Typography>

      {/* Add Todo Form */}
      <Box sx={{ p: 4 }} borderWidth={1} borderRadius="md" sx={{ mb: 6 }} bg="gray.50">
        <Stack spacing={3}>
          <TextField variant="outlined"
            placeholder="Tiêu đề công việc..."
            value={title}
            onChange={e => setTitle(e.target.value)}
          />
          <Typography variant="body1"area
            placeholder="Mô tả chi tiết (tùy chọn)..."
            value={description}
            onChange={e => setDescription(e.target.value)}
            rows={2}
          />
          <Stack direction="row" spacing={3}>
            <Select value={type} onChange={e => setType(e.target.value)}>
              <option value="normal">Thường</option>
              <option value="study">Học tập</option>
              <option value="exam">Thi cử</option>
            </Select>
            <TextField variant="outlined"
              type="datetime-local"
              value={deadline}
              onChange={e => setDeadline(e.target.value)}
              placeholder="Deadline"
            />
            <Button color="primary" onClick={handleAdd} minW="100px">
              Thêm
            </Button>
          </Stack>
        </Stack>
      </Box>

      {/* Todo List */}
      <Stack spacing={3}>
        {todos.length === 0 ? (
          <Typography variant="body1" textAlign="center" color="gray.500" py={8}>
            Chưa có công việc nào. Hãy thêm công việc đầu tiên!
          </Typography>
        ) : (
          todos.map(todo => (
            <Box
              key={todo._id}
              sx={{ p: 4 }}
              borderWidth={1}
              borderRadius="md"
              bg={todo.isDone ? "green.50" : "white"}
              opacity={todo.isDone ? 0.7 : 1}
            >
              <Stack direction="row" justify="space-between" align="flex-start">
                <Stack direction="row" align="flex-start" flex={1}>
                  <Checkbox
                    isChecked={todo.isDone}
                    onChange={() => handleToggle(todo._id, todo.isDone)}
                    sx={{ mt: 1 }}
                  />
                  <Box flex={1}>
                    <Typography variant="body1"
                      fontWeight="bold"
                      textDecoration={todo.isDone ? "line-through" : "none"}
                    >
                      {todo.title}
                    </Typography>
                    {todo.description && (
                      <Typography variant="body1" fontSize="sm" color="gray.600" sx={{ mt: 1 }}>
                        {todo.description}
                      </Typography>
                    )}
                    <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                      <Chip colorScheme={getTypeColor(todo.type)}>
                        {todo.type === 'normal' ? 'Thường' :
                         todo.type === 'study' ? 'Học tập' : 'Thi cử'}
                      </Chip>
                      {todo.deadline && (
                        <Chip color="primary">
                          {new Date(todo.deadline).toLocaleDateString('vi-VN')}
                        </Chip>
                      )}
                    </Stack>
                  </Box>
                </Stack>
                <Button
                  color="primary"
                  size="sm"
                  onClick={() => handleDelete(todo._id)}
                >
                  Xóa
                </Button>
              </Stack>
            </Box>
          ))
        )}
      </Stack>
    </Box>
  );
}