import {
 Head<PERSON>, But<PERSON>, Stack, Box, Text, Input, Checkbox, Badge, Select, Textarea 
} from '../components/ChakraToMui';
import { useEffect, useState } from 'react';
import { getTodos, addTodo, deleteTodo, updateTodo } from '../api';
import { useToast } from '../components/ToastProvider';

export default function ToDo() {
  const [todos, setTodos] = useState([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState('normal');
  const [deadline, setDeadline] = useState('');
  const toast = useToast();

  const fetchTodos = async () => {
    try {
      const res = await getTodos();
      setTodos(res.data);
    } catch {
      setTodos([]);
    }
  };

  useEffect(() => { fetchTodos(); }, []);

  const handleAdd = async () => {
    if (!title) return;
    try {
      const todoData = {
        title,
        description,
        type,
        deadline: deadline ? new Date(deadline) : null
      };
      await addTodo(todoData);
      setTitle('');
      setDescription('');
      setType('normal');
      setDeadline('');
      fetchTodos();
      toast({ title: 'Thêm to-do thành công', status: 'success' });
    } catch {
      toast({ title: 'Thêm to-do thất bại', status: 'error' });
    }
  };

  const handleToggle = async (id, isDone) => {
    try {
      await updateTodo(id, { isDone: !isDone });
      fetchTodos();
    } catch {
      toast({ title: 'Cập nhật thất bại', status: 'error' });
    }
  };

  const handleDelete = async id => {
    try {
      await deleteTodo(id);
      fetchTodos();
      toast({ title: 'Xóa thành công', status: 'success' });
    } catch {
      toast({ title: 'Xóa thất bại', status: 'error' });
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'exam': return 'red';
      case 'study': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <Box maxW="800px" mx="auto" p={4}>
      <Heading mb={6}>To-Do List AI</Heading>

      {/* Add Todo Form */}
      <Box p={4} borderWidth={1} borderRadius="md" mb={6} bg="gray.50">
        <Stack spacing={3}>
          <Input
            placeholder="Tiêu đề công việc..."
            value={title}
            onChange={e => setTitle(e.target.value)}
          />
          <Textarea
            placeholder="Mô tả chi tiết (tùy chọn)..."
            value={description}
            onChange={e => setDescription(e.target.value)}
            rows={2}
          />
          <Stack direction="row" spacing={3}>
            <Select value={type} onChange={e => setType(e.target.value)}>
              <option value="normal">Thường</option>
              <option value="study">Học tập</option>
              <option value="exam">Thi cử</option>
            </Select>
            <Input
              type="datetime-local"
              value={deadline}
              onChange={e => setDeadline(e.target.value)}
              placeholder="Deadline"
            />
            <Button colorScheme="teal" onClick={handleAdd} minW="100px">
              Thêm
            </Button>
          </Stack>
        </Stack>
      </Box>

      {/* Todo List */}
      <Stack spacing={3}>
        {todos.length === 0 ? (
          <Text textAlign="center" color="gray.500" py={8}>
            Chưa có công việc nào. Hãy thêm công việc đầu tiên!
          </Text>
        ) : (
          todos.map(todo => (
            <Box
              key={todo._id}
              p={4}
              borderWidth={1}
              borderRadius="md"
              bg={todo.isDone ? "green.50" : "white"}
              opacity={todo.isDone ? 0.7 : 1}
            >
              <Stack direction="row" justify="space-between" align="flex-start">
                <Stack direction="row" align="flex-start" flex={1}>
                  <Checkbox
                    isChecked={todo.isDone}
                    onChange={() => handleToggle(todo._id, todo.isDone)}
                    mt={1}
                  />
                  <Box flex={1}>
                    <Text
                      fontWeight="bold"
                      textDecoration={todo.isDone ? "line-through" : "none"}
                    >
                      {todo.title}
                    </Text>
                    {todo.description && (
                      <Text fontSize="sm" color="gray.600" mt={1}>
                        {todo.description}
                      </Text>
                    )}
                    <Stack direction="row" spacing={2} mt={2}>
                      <Badge colorScheme={getTypeColor(todo.type)}>
                        {todo.type === 'normal' ? 'Thường' :
                         todo.type === 'study' ? 'Học tập' : 'Thi cử'}
                      </Badge>
                      {todo.deadline && (
                        <Badge colorScheme="orange">
                          {new Date(todo.deadline).toLocaleDateString('vi-VN')}
                        </Badge>
                      )}
                    </Stack>
                  </Box>
                </Stack>
                <Button
                  colorScheme="red"
                  size="sm"
                  onClick={() => handleDelete(todo._id)}
                >
                  Xóa
                </Button>
              </Stack>
            </Box>
          ))
        )}
      </Stack>
    </Box>
  );
}