import React, { useEffect, useState, useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Box,
  TextField,
  Checkbox,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Container,
  Paper,
  IconButton,
  FormControlLabel,
  Grid,
  Tabs,
  Tab,
  Badge,
  LinearProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Card,
  CardContent,
  Divider,
  Avatar,
  AvatarGroup,
  Switch,
  Slider,
  Rating,
  Autocomplete,
  ToggleButton,
  ToggleButtonGroup,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckIcon,
  Login as LoginIcon,
  Edit as EditIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Schedule as ScheduleIcon,
  Flag as FlagIcon,
  Category as CategoryIcon,
  Timer as TimerIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  Code as CodeIcon,
  Science as ScienceIcon,
  MenuBook as BookIcon,
  Psychology as PsychologyIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  ViewKanban as KanbanIcon,
  ViewList as ListIcon,
  CalendarToday as CalendarIcon,
  Notifications as NotificationIcon,
  Group as GroupIcon,
  AttachFile as AttachIcon,
  Comment as CommentIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { getTodos, addTodo, deleteTodo, updateTodo } from '../api';
import { useToast } from '../components/ToastProvider';
import { useAuth } from '../App';

export default function ToDo() {
  const { user } = useAuth();
  const [todos, setTodos] = useState([]);
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  // Form states
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState('normal');
  const [priority, setPriority] = useState('medium');
  const [deadline, setDeadline] = useState('');
  const [estimatedTime, setEstimatedTime] = useState(60);
  const [tags, setTags] = useState([]);
  const [difficulty, setDifficulty] = useState(3);
  const [isImportant, setIsImportant] = useState(false);

  // View states
  const [viewMode, setViewMode] = useState('list'); // list, kanban, calendar
  const [currentTab, setCurrentTab] = useState(0); // 0: All, 1: Today, 2: Upcoming, 3: Completed
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('deadline');
  const [showCompleted, setShowCompleted] = useState(true);

  // Dialog states
  const [editDialog, setEditDialog] = useState(false);
  const [editingTodo, setEditingTodo] = useState(null);
  const [detailDialog, setDetailDialog] = useState(false);
  const [selectedTodo, setSelectedTodo] = useState(null);

  // Advanced features
  const [studyMode, setStudyMode] = useState(false);
  const [pomodoroTimer, setPomodoroTimer] = useState(null);
  const [studyStreak, setStudyStreak] = useState(0);
  const [weeklyGoal, setWeeklyGoal] = useState(10);

  // Computed values
  const filteredTodos = useMemo(() => {
    let filtered = todos;

    // Filter by tab
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    switch (currentTab) {
      case 1: // Today
        filtered = filtered.filter(todo => {
          const deadline = new Date(todo.deadline);
          return deadline.toDateString() === today.toDateString();
        });
        break;
      case 2: // Upcoming
        filtered = filtered.filter(todo => {
          const deadline = new Date(todo.deadline);
          return deadline > today && !todo.isDone;
        });
        break;
      case 3: // Completed
        filtered = filtered.filter(todo => todo.isDone);
        break;
      default: // All
        break;
    }

    // Filter by type
    if (filterBy !== 'all') {
      filtered = filtered.filter(todo => todo.type === filterBy);
    }

    // Hide/show completed
    if (!showCompleted) {
      filtered = filtered.filter(todo => !todo.isDone);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'deadline':
          return new Date(a.deadline || '9999-12-31') - new Date(b.deadline || '9999-12-31');
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority] || 2) - (priorityOrder[a.priority] || 2);
        case 'difficulty':
          return (b.difficulty || 3) - (a.difficulty || 3);
        case 'created':
          return new Date(b.createdAt) - new Date(a.createdAt);
        default:
          return 0;
      }
    });

    return filtered;
  }, [todos, currentTab, filterBy, showCompleted, sortBy]);

  const stats = useMemo(() => {
    const total = todos.length;
    const completed = todos.filter(t => t.isDone).length;
    const overdue = todos.filter(t => {
      const deadline = new Date(t.deadline);
      return deadline < new Date() && !t.isDone;
    }).length;
    const today = todos.filter(t => {
      const deadline = new Date(t.deadline);
      return deadline.toDateString() === new Date().toDateString();
    }).length;

    return { total, completed, overdue, today, completionRate: total > 0 ? (completed / total) * 100 : 0 };
  }, [todos]);

  // Check if user is authenticated
  if (!user) {
    return (
      <Box sx={{
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        minHeight: '100vh',
        py: 4
      }}>
        <Container maxWidth="md">
          <Paper elevation={3} sx={{ p: 6, borderRadius: 3, textAlign: 'center' }}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              🔐 Cần đăng nhập
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
              Bạn cần đăng nhập để sử dụng tính năng quản lý công việc
            </Typography>
            <Button
              component={RouterLink}
              to="/login"
              variant="contained"
              size="large"
              startIcon={<LoginIcon />}
              sx={{ borderRadius: 2 }}
            >
              Đăng nhập ngay
            </Button>
          </Paper>
        </Container>
      </Box>
    );
  }

  const fetchTodos = async () => {
    try {
      setLoading(true);
      const res = await getTodos();
      setTodos(Array.isArray(res.data) ? res.data : []);
    } catch (error) {
      console.error('Error fetching todos:', error);
      setTodos([]);
      toast({
        title: 'Lỗi',
        description: 'Không thể tải danh sách công việc',
        status: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async () => {
    if (!title.trim()) {
      toast({
        title: 'Lỗi',
        description: 'Vui lòng nhập tiêu đề nhiệm vụ',
        status: 'warning'
      });
      return;
    }

    try {
      const newTodo = {
        title: title.trim(),
        description: description.trim(),
        type,
        priority,
        deadline: deadline || null,
        estimatedTime,
        tags,
        difficulty,
        isImportant,
        isDone: false
      };

      const res = await addTodo(newTodo);
      setTodos(prev => [...prev, res.data]);

      // Reset form
      setTitle('');
      setDescription('');
      setType('normal');
      setPriority('medium');
      setDeadline('');
      setEstimatedTime(60);
      setTags([]);
      setDifficulty(3);
      setIsImportant(false);

      toast({
        title: '🎉 Thành công',
        description: 'Đã tạo nhiệm vụ mới!',
        status: 'success'
      });
    } catch (error) {
      console.error('Error adding todo:', error);
      toast({
        title: '❌ Lỗi',
        description: 'Không thể tạo nhiệm vụ',
        status: 'error'
      });
    }
  };

  const handleToggle = async (id, currentStatus) => {
    try {
      const res = await updateTodo(id, { isDone: !currentStatus });
      setTodos(prev => prev.map(todo => 
        todo._id === id ? { ...todo, isDone: !currentStatus } : todo
      ));
      
      toast({
        title: 'Thành công',
        description: !currentStatus ? 'Đã hoàn thành công việc' : 'Đã bỏ hoàn thành',
        status: 'success'
      });
    } catch (error) {
      console.error('Error updating todo:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể cập nhật công việc',
        status: 'error'
      });
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteTodo(id);
      setTodos(prev => prev.filter(todo => todo._id !== id));
      
      toast({
        title: 'Thành công',
        description: 'Đã xóa công việc',
        status: 'success'
      });
    } catch (error) {
      console.error('Error deleting todo:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể xóa công việc',
        status: 'error'
      });
    }
  };

  useEffect(() => {
    fetchTodos();
  }, []);

  const getTypeColor = (type) => {
    switch (type) {
      case 'exam': return 'error';
      case 'assignment': return 'warning';
      case 'project': return 'info';
      case 'study': return 'success';
      default: return 'default';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'exam': return 'Thi cử';
      case 'assignment': return 'Bài tập';
      case 'project': return 'Dự án';
      case 'study': return 'Học tập';
      default: return 'Thường';
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      py: 2
    }}>
      <Container maxWidth="xl">
        <Stack spacing={3}>
          {/* Smart Header with Stats */}
          <Paper elevation={4} sx={{ p: 4, borderRadius: 4, background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)' }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                    <AssignmentIcon fontSize="large" />
                  </Avatar>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" gutterBottom>
                      📚 StudyFlow - Todo Manager
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Hệ thống quản lý học tập thông minh cho sinh viên
                    </Typography>
                  </Box>
                </Stack>
              </Grid>

              <Grid item xs={12} md={6}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                      <Typography variant="h6" color="primary.main" fontWeight="bold">
                        {stats.total}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Tổng số
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={3}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                      <Typography variant="h6" color="success.main" fontWeight="bold">
                        {stats.completed}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Hoàn thành
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={3}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                      <Typography variant="h6" color="warning.main" fontWeight="bold">
                        {stats.today}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Hôm nay
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={3}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                      <Typography variant="h6" color="error.main" fontWeight="bold">
                        {stats.overdue}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Quá hạn
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                {/* Progress Bar */}
                <Box sx={{ mt: 2 }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Tiến độ hoàn thành
                    </Typography>
                    <Typography variant="body2" fontWeight="bold" color="primary.main">
                      {stats.completionRate.toFixed(0)}%
                    </Typography>
                  </Stack>
                  <LinearProgress
                    variant="determinate"
                    value={stats.completionRate}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        background: 'linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%)'
                      }
                    }}
                  />
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Advanced Add Todo Form */}
          <Paper elevation={4} sx={{ p: 4, borderRadius: 4, background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)' }}>
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <AddIcon color="primary" />
                  <Typography variant="h6" fontWeight="bold">
                    ✨ Tạo nhiệm vụ mới
                  </Typography>
                </Stack>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  {/* Row 1: Basic Info */}
                  <Grid item xs={12} md={8}>
                    <TextField
                      fullWidth
                      label="📝 Tiêu đề nhiệm vụ"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      variant="outlined"
                      required
                      placeholder="VD: Hoàn thành bài tập Toán cao cấp"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>🎯 Độ ưu tiên</InputLabel>
                      <Select
                        value={priority}
                        label="🎯 Độ ưu tiên"
                        onChange={(e) => setPriority(e.target.value)}
                        sx={{ borderRadius: 2 }}
                      >
                        <MenuItem value="low">🟢 Thấp</MenuItem>
                        <MenuItem value="medium">🟡 Trung bình</MenuItem>
                        <MenuItem value="high">🟠 Cao</MenuItem>
                        <MenuItem value="urgent">🔴 Khẩn cấp</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Row 2: Category and Type */}
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>📚 Loại nhiệm vụ</InputLabel>
                      <Select
                        value={type}
                        label="📚 Loại nhiệm vụ"
                        onChange={(e) => setType(e.target.value)}
                        sx={{ borderRadius: 2 }}
                      >
                        <MenuItem value="normal">📄 Thường</MenuItem>
                        <MenuItem value="study">📖 Học tập</MenuItem>
                        <MenuItem value="exam">🎓 Thi cử</MenuItem>
                        <MenuItem value="assignment">✏️ Bài tập</MenuItem>
                        <MenuItem value="project">🚀 Dự án</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="⏰ Hạn chót"
                      type="datetime-local"
                      value={deadline}
                      onChange={(e) => setDeadline(e.target.value)}
                      InputLabelProps={{ shrink: true }}
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>

                  {/* Row 3: Advanced Settings */}
                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>⏱️ Thời gian ước tính (phút)</Typography>
                    <Slider
                      value={estimatedTime}
                      onChange={(e, value) => setEstimatedTime(value)}
                      min={15}
                      max={480}
                      step={15}
                      marks={[
                        { value: 30, label: '30p' },
                        { value: 120, label: '2h' },
                        { value: 240, label: '4h' },
                        { value: 480, label: '8h' }
                      ]}
                      valueLabelDisplay="auto"
                      valueLabelFormat={(value) => `${value}p`}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>🎚️ Độ khó (1-5)</Typography>
                    <Rating
                      value={difficulty}
                      onChange={(e, value) => setDifficulty(value)}
                      max={5}
                      size="large"
                      icon={<StarIcon fontSize="inherit" />}
                      emptyIcon={<StarBorderIcon fontSize="inherit" />}
                    />
                  </Grid>

                  {/* Row 4: Description and Tags */}
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="📋 Mô tả chi tiết"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      multiline
                      rows={3}
                      variant="outlined"
                      placeholder="Mô tả chi tiết về nhiệm vụ, yêu cầu, ghi chú..."
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>

                  <Grid item xs={12} md={8}>
                    <Autocomplete
                      multiple
                      freeSolo
                      options={['Toán', 'Lý', 'Hóa', 'Văn', 'Anh', 'Lịch sử', 'Địa lý', 'Sinh học']}
                      value={tags}
                      onChange={(e, value) => setTags(value)}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={option}
                            {...getTagProps({ index })}
                            key={index}
                          />
                        ))
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="🏷️ Tags"
                          placeholder="Thêm tags..."
                          sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={isImportant}
                          onChange={(e) => setIsImportant(e.target.checked)}
                          color="warning"
                        />
                      }
                      label="⭐ Đánh dấu quan trọng"
                    />
                  </Grid>

                  {/* Action Buttons */}
                  <Grid item xs={12}>
                    <Stack direction="row" spacing={2}>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleAdd}
                        size="large"
                        sx={{
                          borderRadius: 3,
                          px: 4,
                          background: 'linear-gradient(45deg, #667eea, #764ba2)',
                          '&:hover': {
                            background: 'linear-gradient(45deg, #5a6fd8, #6a4190)'
                          }
                        }}
                      >
                        Tạo nhiệm vụ
                      </Button>

                      <Button
                        variant="outlined"
                        onClick={() => {
                          setTitle('');
                          setDescription('');
                          setType('normal');
                          setPriority('medium');
                          setDeadline('');
                          setEstimatedTime(60);
                          setTags([]);
                          setDifficulty(3);
                          setIsImportant(false);
                        }}
                        sx={{ borderRadius: 3, px: 3 }}
                      >
                        Đặt lại
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Paper>

          {/* Smart Tabs and Filters */}
          <Paper elevation={4} sx={{ borderRadius: 4, overflow: 'hidden' }}>
            {/* Tabs */}
            <Tabs
              value={currentTab}
              onChange={(e, value) => setCurrentTab(value)}
              variant="fullWidth"
              sx={{
                bgcolor: 'primary.main',
                '& .MuiTab-root': {
                  color: 'white',
                  fontWeight: 'bold',
                  '&.Mui-selected': {
                    color: 'yellow',
                    bgcolor: 'rgba(255,255,255,0.1)'
                  }
                }
              }}
            >
              <Tab
                label={
                  <Badge badgeContent={stats.total} color="secondary">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ListIcon />
                      Tất cả
                    </Box>
                  </Badge>
                }
              />
              <Tab
                label={
                  <Badge badgeContent={stats.today} color="warning">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CalendarIcon />
                      Hôm nay
                    </Box>
                  </Badge>
                }
              />
              <Tab
                label={
                  <Badge badgeContent={filteredTodos.filter(t => !t.isDone).length} color="info">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ScheduleIcon />
                      Sắp tới
                    </Box>
                  </Badge>
                }
              />
              <Tab
                label={
                  <Badge badgeContent={stats.completed} color="success">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckIcon />
                      Hoàn thành
                    </Box>
                  </Badge>
                }
              />
            </Tabs>

            {/* Filters and Controls */}
            <Box sx={{ p: 3, bgcolor: 'grey.50', borderBottom: 1, borderColor: 'divider' }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>🔍 Lọc theo loại</InputLabel>
                    <Select
                      value={filterBy}
                      label="🔍 Lọc theo loại"
                      onChange={(e) => setFilterBy(e.target.value)}
                    >
                      <MenuItem value="all">Tất cả</MenuItem>
                      <MenuItem value="study">📖 Học tập</MenuItem>
                      <MenuItem value="exam">🎓 Thi cử</MenuItem>
                      <MenuItem value="assignment">✏️ Bài tập</MenuItem>
                      <MenuItem value="project">🚀 Dự án</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>📊 Sắp xếp</InputLabel>
                    <Select
                      value={sortBy}
                      label="📊 Sắp xếp"
                      onChange={(e) => setSortBy(e.target.value)}
                    >
                      <MenuItem value="deadline">⏰ Hạn chót</MenuItem>
                      <MenuItem value="priority">🎯 Độ ưu tiên</MenuItem>
                      <MenuItem value="difficulty">🎚️ Độ khó</MenuItem>
                      <MenuItem value="created">📅 Ngày tạo</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3}>
                  <ToggleButtonGroup
                    value={viewMode}
                    exclusive
                    onChange={(e, value) => value && setViewMode(value)}
                    size="small"
                  >
                    <ToggleButton value="list">
                      <ListIcon />
                    </ToggleButton>
                    <ToggleButton value="kanban">
                      <KanbanIcon />
                    </ToggleButton>
                    <ToggleButton value="calendar">
                      <CalendarIcon />
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Grid>

                <Grid item xs={12} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showCompleted}
                        onChange={(e) => setShowCompleted(e.target.checked)}
                        size="small"
                      />
                    }
                    label="Hiện đã hoàn thành"
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Todo List Content */}
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
                📝 {filteredTodos.length} nhiệm vụ
              </Typography>
            
              {loading ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <CircularProgress size={60} />
                  <Typography variant="h6" sx={{ mt: 2 }}>
                    Đang tải nhiệm vụ...
                  </Typography>
                </Box>
              ) : filteredTodos.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 12 }}>
                  <Typography variant="h5" color="text.secondary" gutterBottom>
                    🎯 Không có nhiệm vụ nào
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {currentTab === 0 ? 'Hãy tạo nhiệm vụ đầu tiên!' : 'Không có nhiệm vụ trong mục này'}
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={3}>
                  {filteredTodos.map(todo => (
                    <Grid item xs={12} key={todo._id}>
                      <Card
                        elevation={todo.isDone ? 1 : 3}
                        sx={{
                          borderRadius: 3,
                          border: todo.isImportant ? 2 : 1,
                          borderColor: todo.isImportant ? 'warning.main' : 'divider',
                          bgcolor: todo.isDone ? 'grey.50' : 'background.paper',
                          opacity: todo.isDone ? 0.8 : 1,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: todo.isDone ? 'none' : 'translateY(-2px)',
                            boxShadow: todo.isDone ? 1 : 6
                          },
                          position: 'relative',
                          overflow: 'visible'
                        }}
                      >
                        {/* Priority Indicator */}
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: 4,
                            height: '100%',
                            bgcolor:
                              todo.priority === 'urgent' ? 'error.main' :
                              todo.priority === 'high' ? 'warning.main' :
                              todo.priority === 'medium' ? 'info.main' : 'success.main'
                          }}
                        />

                        <CardContent sx={{ p: 3, pl: 4 }}>
                          <Stack direction="row" alignItems="flex-start" spacing={2}>
                            {/* Checkbox */}
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={todo.isDone}
                                  onChange={() => handleToggle(todo._id, todo.isDone)}
                                  color="success"
                                  size="large"
                                />
                              }
                              label=""
                              sx={{ mt: -1 }}
                            />

                            {/* Main Content */}
                            <Box sx={{ flex: 1 }}>
                              {/* Title and Important Star */}
                              <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                                <Typography
                                  variant="h6"
                                  fontWeight="bold"
                                  sx={{
                                    textDecoration: todo.isDone ? 'line-through' : 'none',
                                    color: todo.isDone ? 'text.secondary' : 'text.primary',
                                    flex: 1
                                  }}
                                >
                                  {todo.title}
                                </Typography>

                                {todo.isImportant && (
                                  <StarIcon color="warning" />
                                )}

                                {/* Difficulty Rating */}
                                <Rating
                                  value={todo.difficulty || 3}
                                  readOnly
                                  size="small"
                                  max={5}
                                />
                              </Stack>

                              {/* Description */}
                              {todo.description && (
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  sx={{
                                    mb: 2,
                                    textDecoration: todo.isDone ? 'line-through' : 'none'
                                  }}
                                >
                                  {todo.description}
                                </Typography>
                              )}

                              {/* Tags and Metadata */}
                              <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 2 }}>
                                {/* Type Chip */}
                                <Chip
                                  label={getTypeLabel(todo.type)}
                                  size="small"
                                  color={getTypeColor(todo.type)}
                                  variant={todo.isDone ? 'outlined' : 'filled'}
                                  icon={
                                    todo.type === 'study' ? <BookIcon /> :
                                    todo.type === 'exam' ? <SchoolIcon /> :
                                    todo.type === 'assignment' ? <AssignmentIcon /> :
                                    todo.type === 'project' ? <CodeIcon /> : <CategoryIcon />
                                  }
                                />

                                {/* Priority Chip */}
                                <Chip
                                  label={
                                    todo.priority === 'urgent' ? '🔴 Khẩn cấp' :
                                    todo.priority === 'high' ? '🟠 Cao' :
                                    todo.priority === 'medium' ? '🟡 Trung bình' : '🟢 Thấp'
                                  }
                                  size="small"
                                  variant="outlined"
                                />

                                {/* Deadline Chip */}
                                {todo.deadline && (
                                  <Chip
                                    label={`⏰ ${new Date(todo.deadline).toLocaleDateString('vi-VN')}`}
                                    size="small"
                                    variant="outlined"
                                    color={
                                      new Date(todo.deadline) < new Date() && !todo.isDone ? 'error' :
                                      new Date(todo.deadline).toDateString() === new Date().toDateString() ? 'warning' : 'primary'
                                    }
                                  />
                                )}

                                {/* Estimated Time */}
                                {todo.estimatedTime && (
                                  <Chip
                                    label={`⏱️ ${todo.estimatedTime}p`}
                                    size="small"
                                    variant="outlined"
                                    color="info"
                                  />
                                )}

                                {/* Tags */}
                                {todo.tags?.map((tag, index) => (
                                  <Chip
                                    key={index}
                                    label={`#${tag}`}
                                    size="small"
                                    variant="outlined"
                                    color="secondary"
                                  />
                                ))}

                                {/* Completion Status */}
                                {todo.isDone && (
                                  <Chip
                                    label="✅ Hoàn thành"
                                    size="small"
                                    color="success"
                                    variant="filled"
                                  />
                                )}
                              </Stack>

                              {/* Progress Bar for Time */}
                              {todo.estimatedTime && todo.actualTime && (
                                <Box sx={{ mb: 1 }}>
                                  <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 0.5 }}>
                                    <Typography variant="caption" color="text.secondary">
                                      Tiến độ thời gian
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {todo.actualTime}/{todo.estimatedTime} phút
                                    </Typography>
                                  </Stack>
                                  <LinearProgress
                                    variant="determinate"
                                    value={Math.min((todo.actualTime / todo.estimatedTime) * 100, 100)}
                                    sx={{ height: 4, borderRadius: 2 }}
                                    color={todo.actualTime > todo.estimatedTime ? 'warning' : 'primary'}
                                  />
                                </Box>
                              )}
                            </Box>

                            {/* Action Buttons */}
                            <Stack direction="column" spacing={1}>
                              <Tooltip title="Chỉnh sửa">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setEditingTodo(todo);
                                    setEditDialog(true);
                                  }}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>

                              <Tooltip title="Chi tiết">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setSelectedTodo(todo);
                                    setDetailDialog(true);
                                  }}
                                >
                                  <HistoryIcon />
                                </IconButton>
                              </Tooltip>

                              <Tooltip title="Xóa">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDelete(todo._id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Stack>
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>
          </Paper>

          {/* Study Mode Panel */}
          {studyMode && (
            <Paper elevation={4} sx={{ p: 4, borderRadius: 4, bgcolor: 'success.50' }}>
              <Stack direction="row" alignItems="center" spacing={2}>
                <PsychologyIcon color="success" fontSize="large" />
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" fontWeight="bold" color="success.main">
                    🧠 Chế độ học tập đang bật
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tập trung hoàn thành nhiệm vụ một cách hiệu quả
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  color="success"
                  onClick={() => setStudyMode(false)}
                >
                  Tắt chế độ
                </Button>
              </Stack>
            </Paper>
          )}

          {/* Quick Actions */}
          <Paper elevation={4} sx={{ p: 3, borderRadius: 4, bgcolor: 'primary.50' }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              ⚡ Thao tác nhanh
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PsychologyIcon />}
                  onClick={() => setStudyMode(!studyMode)}
                  color={studyMode ? 'success' : 'primary'}
                >
                  {studyMode ? 'Tắt' : 'Bật'} Study Mode
                </Button>
              </Grid>
              <Grid item xs={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<TimerIcon />}
                  onClick={() => {
                    toast({
                      title: '⏰ Pomodoro Timer',
                      description: 'Tính năng sẽ sớm có!',
                      status: 'info'
                    });
                  }}
                >
                  Pomodoro
                </Button>
              </Grid>
              <Grid item xs={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<TrendingUpIcon />}
                  onClick={() => {
                    toast({
                      title: '📊 Thống kê',
                      description: `Hoàn thành: ${stats.completionRate.toFixed(0)}%`,
                      status: 'success'
                    });
                  }}
                >
                  Thống kê
                </Button>
              </Grid>
              <Grid item xs={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<NotificationIcon />}
                  onClick={() => {
                    toast({
                      title: '🔔 Nhắc nhở',
                      description: 'Đã bật thông báo!',
                      status: 'info'
                    });
                  }}
                >
                  Nhắc nhở
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Stack>
      </Container>
    </Box>
  );
}
