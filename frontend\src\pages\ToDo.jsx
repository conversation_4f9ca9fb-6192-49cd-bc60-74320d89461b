import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  TextField,
  Checkbox,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Container,
  Paper,
  IconButton,
  FormControlLabel,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import { getTodos, addTodo, deleteTodo, updateTodo } from '../api';
import { useToast } from '../components/ToastProvider';

export default function ToDo() {
  const [todos, setTodos] = useState([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState('normal');
  const [deadline, setDeadline] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const fetchTodos = async () => {
    try {
      setLoading(true);
      const res = await getTodos();
      setTodos(Array.isArray(res.data) ? res.data : []);
    } catch (error) {
      console.error('Error fetching todos:', error);
      setTodos([]);
      toast({
        title: 'Lỗi',
        description: 'Không thể tải danh sách công việc',
        status: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async () => {
    if (!title.trim()) {
      toast({
        title: 'Lỗi',
        description: 'Vui lòng nhập tiêu đề công việc',
        status: 'warning'
      });
      return;
    }

    try {
      const newTodo = {
        title: title.trim(),
        description: description.trim(),
        type,
        deadline: deadline || null,
        isDone: false
      };

      const res = await addTodo(newTodo);
      setTodos(prev => [...prev, res.data]);
      
      // Reset form
      setTitle('');
      setDescription('');
      setType('normal');
      setDeadline('');
      
      toast({
        title: 'Thành công',
        description: 'Đã thêm công việc mới',
        status: 'success'
      });
    } catch (error) {
      console.error('Error adding todo:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể thêm công việc',
        status: 'error'
      });
    }
  };

  const handleToggle = async (id, currentStatus) => {
    try {
      const res = await updateTodo(id, { isDone: !currentStatus });
      setTodos(prev => prev.map(todo => 
        todo._id === id ? { ...todo, isDone: !currentStatus } : todo
      ));
      
      toast({
        title: 'Thành công',
        description: !currentStatus ? 'Đã hoàn thành công việc' : 'Đã bỏ hoàn thành',
        status: 'success'
      });
    } catch (error) {
      console.error('Error updating todo:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể cập nhật công việc',
        status: 'error'
      });
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteTodo(id);
      setTodos(prev => prev.filter(todo => todo._id !== id));
      
      toast({
        title: 'Thành công',
        description: 'Đã xóa công việc',
        status: 'success'
      });
    } catch (error) {
      console.error('Error deleting todo:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể xóa công việc',
        status: 'error'
      });
    }
  };

  useEffect(() => {
    fetchTodos();
  }, []);

  const getTypeColor = (type) => {
    switch (type) {
      case 'urgent': return 'error';
      case 'important': return 'warning';
      case 'study': return 'info';
      default: return 'default';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'urgent': return 'Khẩn cấp';
      case 'important': return 'Quan trọng';
      case 'study': return 'Học tập';
      default: return 'Thường';
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      minHeight: '100vh',
      py: 4
    }}>
      <Container maxWidth="lg">
        <Stack spacing={4}>
          {/* Header */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              📋 Quản lý công việc
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Tổ chức và theo dõi các công việc của bạn một cách hiệu quả
            </Typography>
          </Paper>

          {/* Add Todo Form */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
              ➕ Thêm công việc mới
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tiêu đề công việc"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  variant="outlined"
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Loại công việc</InputLabel>
                  <Select
                    value={type}
                    label="Loại công việc"
                    onChange={(e) => setType(e.target.value)}
                  >
                    <MenuItem value="normal">Thường</MenuItem>
                    <MenuItem value="important">Quan trọng</MenuItem>
                    <MenuItem value="urgent">Khẩn cấp</MenuItem>
                    <MenuItem value="study">Học tập</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Hạn chót"
                  type="date"
                  value={deadline}
                  onChange={(e) => setDeadline(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Mô tả (tùy chọn)"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  multiline
                  rows={2}
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAdd}
                  size="large"
                  sx={{ borderRadius: 2 }}
                >
                  Thêm công việc
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Todo List */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
              📝 Danh sách công việc ({todos.length})
            </Typography>
            
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography>Đang tải...</Typography>
              </Box>
            ) : todos.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <Typography variant="body1" color="text.secondary">
                  Chưa có công việc nào. Hãy thêm công việc đầu tiên! 🚀
                </Typography>
              </Box>
            ) : (
              <Stack spacing={2}>
                {todos.map(todo => (
                  <Paper
                    key={todo._id}
                    elevation={1}
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      bgcolor: todo.isDone ? 'success.50' : 'background.paper',
                      opacity: todo.isDone ? 0.8 : 1,
                      border: 1,
                      borderColor: todo.isDone ? 'success.main' : 'divider',
                      transition: 'all 0.2s'
                    }}
                  >
                    <Stack direction="row" alignItems="flex-start" spacing={2}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={todo.isDone}
                            onChange={() => handleToggle(todo._id, todo.isDone)}
                            color="success"
                          />
                        }
                        label=""
                      />
                      
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="h6"
                          fontWeight="bold"
                          sx={{
                            textDecoration: todo.isDone ? 'line-through' : 'none',
                            color: todo.isDone ? 'text.secondary' : 'text.primary'
                          }}
                        >
                          {todo.title}
                        </Typography>
                        
                        {todo.description && (
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              mt: 1,
                              textDecoration: todo.isDone ? 'line-through' : 'none'
                            }}
                          >
                            {todo.description}
                          </Typography>
                        )}
                        
                        <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
                          <Chip
                            label={getTypeLabel(todo.type)}
                            size="small"
                            color={getTypeColor(todo.type)}
                            variant={todo.isDone ? 'outlined' : 'filled'}
                          />
                          
                          {todo.deadline && (
                            <Chip
                              label={`📅 ${new Date(todo.deadline).toLocaleDateString('vi-VN')}`}
                              size="small"
                              variant="outlined"
                              color="primary"
                            />
                          )}
                          
                          {todo.isDone && (
                            <Chip
                              label="✅ Hoàn thành"
                              size="small"
                              color="success"
                              variant="outlined"
                            />
                          )}
                        </Stack>
                      </Box>
                      
                      <IconButton
                        color="error"
                        onClick={() => handleDelete(todo._id)}
                        size="small"
                        sx={{ mt: 1 }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Stack>
                  </Paper>
                ))}
              </Stack>
            )}
          </Paper>
        </Stack>
      </Container>
    </Box>
  );
}
