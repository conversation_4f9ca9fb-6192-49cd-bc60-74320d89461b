import {
  Typography,
  Select,
  Box,
  Stack,
  Button,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Container,
  Paper
} from '@mui/material';
import { useState, useEffect } from 'react';
import { useToast } from '../components/ToastProvider';
// TODO: import API khi backend có

// Dữ liệu mẫu ngành và quiz
const majors = [
  { value: 'cntt', label: 'Công nghệ thông tin', quizzes: [
    { question: 'HTML là gì?', options: ['Ngôn ngữ lập trình', 'Ngôn ngữ đánh dấu', '<PERSON><PERSON> sở dữ liệu'], answer: 'Ngôn ngữ đánh dấu', explanation: 'HTML là HyperText Markup Language.' },
    { question: 'CSS dùng để làm gì?', options: ['Tạo hiệu ứng', 'Định dạng giao diện', 'Lưu trữ dữ liệu'], answer: 'Định dạng giao diện', explanation: 'CSS là Cascading Style Sheets.' },
  ] },
  { value: 'qtkd', label: 'Quản trị kinh doanh', quizzes: [
    { question: 'Marketing là gì?', options: ['Quản lý tài chính', 'Quảng bá sản phẩm', 'Tuyển dụng nhân sự'], answer: 'Quảng bá sản phẩm', explanation: 'Marketing là hoạt động quảng bá, tiếp thị.' },
    { question: 'KPI là gì?', options: ['Chỉ số đánh giá hiệu suất', 'Chi phí sản xuất', 'Kế hoạch đầu tư'], answer: 'Chỉ số đánh giá hiệu suất', explanation: 'KPI là Key Performance Indicator.' },
  ] },
];

export default function Trial() {
  const [major, setMajor] = useState('');
  const [quiz, setQuiz] = useState([]);
  const [answers, setAnswers] = useState([]);
  const [result, setResult] = useState(null);
  const toast = useToast();

  useEffect(() => {
    if (major) {
      const m = majors.find(m => m.value === major);
      setQuiz(m?.quizzes || []);
      setAnswers(Array(m?.quizzes.length || 0).fill(''));
      setResult(null);
    }
  }, [major]);

  const handleSubmit = () => {
    let correct = 0;
    let feedback = [];
    quiz.forEach((q, i) => {
      if (answers[i] === q.answer) correct++;
      feedback.push({
        question: q.question,
        your: answers[i],
        correct: q.answer,
        explanation: q.explanation,
        isCorrect: answers[i] === q.answer,
      });
    });
    setResult({ correct, total: quiz.length, feedback });
    toast({ title: 'Đã nộp bài, xem kết quả bên dưới', status: 'info' });
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={2} sx={{ p: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Học thử ngành
        </Typography>

        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel>Chọn ngành</InputLabel>
          <Select
            value={major}
            onChange={e => setMajor(e.target.value)}
            label="Chọn ngành"
          >
            {majors.map(m => (
              <MenuItem key={m.value} value={m.value}>
                {m.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {quiz.length > 0 && !result && (
          <Stack spacing={3}>
            {quiz.map((q, i) => (
              <Paper key={i} variant="outlined" sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Câu {i+1}: {q.question}
                </Typography>
                <Stack spacing={1}>
                  {q.options.map(opt => (
                    <Button
                      key={opt}
                      variant={answers[i] === opt ? 'contained' : 'outlined'}
                      color="primary"
                      onClick={() => setAnswers(a => a.map((v, idx) => idx === i ? opt : v))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      {opt}
                    </Button>
                  ))}
                </Stack>
              </Paper>
            ))}
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              size="large"
              sx={{ mt: 2 }}
            >
              Nộp bài kiểm tra
            </Button>
          </Stack>
        )}
        {result && (
          <Paper
            variant="outlined"
            sx={{
              mt: 4,
              p: 3,
              bgcolor: 'primary.50',
              border: '1px solid',
              borderColor: 'primary.200'
            }}
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Kết quả: {result.correct}/{result.total} câu đúng
            </Typography>

            <Stack spacing={2} sx={{ mt: 3 }}>
              {result.feedback.map((f, i) => (
                <Paper
                  key={i}
                  variant="outlined"
                  sx={{
                    p: 2,
                    bgcolor: f.isCorrect ? 'success.50' : 'error.50',
                    border: '1px solid',
                    borderColor: f.isCorrect ? 'success.200' : 'error.200'
                  }}
                >
                  <Typography variant="body1">
                    <strong>Câu hỏi:</strong> {f.question}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    <strong>Đáp án của bạn:</strong> {f.your || 'Chưa chọn'}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    <strong>Đáp án đúng:</strong> {f.correct}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    <strong>Giải thích:</strong> {f.explanation}
                  </Typography>
                </Paper>
              ))}
            </Stack>
          </Paper>
        )}
      </Paper>
    </Container>
  );
} 