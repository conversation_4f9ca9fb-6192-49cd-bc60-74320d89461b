// Temporary file to replace Chakra UI components with Material-UI equivalents
import React from 'react';
import {
  Box as <PERSON>i<PERSON><PERSON>,
  Container as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Stack,
  Grid,
  Card as <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader as <PERSON><PERSON><PERSON>ard<PERSON>ead<PERSON>,
  <PERSON>,
  LinearProgress,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Avatar as MuiAvatar,
  Paper,
  Divider as <PERSON><PERSON><PERSON>ivider,
  TextField,
  Select as MuiSelect,
  MenuItem,
  FormControl,
  InputLabel,
  Switch as MuiS<PERSON>,
  Checkbox as MuiCheckbox,
  Tabs as MuiTabs,
  Tab as MuiT<PERSON>,
  TabPanel as MuiTabPanel,
  Modal as MuiModal,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton as MuiIconButton,
  A<PERSON>rdion as <PERSON>i<PERSON><PERSON>rdion,
  AccordionSummary,
  AccordionDetails,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>nackbar,
  Alert
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Chakra UI to Material-UI component mappings
export const Box = styled(MuiBox)({});
export const Container = styled(MuiContainer)({});
export const Heading = ({ children, ...props }) => (
  <Typography variant="h4" component="h1" {...props}>
    {children}
  </Typography>
);
export const Text = ({ children, ...props }) => (
  <Typography variant="body1" {...props}>
    {children}
  </Typography>
);

export const VStack = ({ children, spacing = 2, ...props }) => (
  <Stack direction="column" spacing={spacing} {...props}>
    {children}
  </Stack>
);

export const HStack = ({ children, spacing = 2, ...props }) => (
  <Stack direction="row" spacing={spacing} {...props}>
    {children}
  </Stack>
);

export const SimpleGrid = ({ children, columns = 1, spacing = 2, ...props }) => (
  <Grid container spacing={spacing} {...props}>
    {React.Children.map(children, (child, index) => (
      <Grid item xs={12} sm={12/columns} key={index}>
        {child}
      </Grid>
    ))}
  </Grid>
);

export const Card = ({ children, ...props }) => (
  <MuiCard {...props}>
    {children}
  </MuiCard>
);

export const CardBody = ({ children, ...props }) => (
  <CardContent {...props}>
    {children}
  </CardContent>
);

export const CardHeader = ({ children, ...props }) => (
  <MuiCardHeader title={children} {...props} />
);

export const Badge = ({ children, colorScheme, ...props }) => (
  <Chip 
    label={children} 
    color={colorScheme === 'green' ? 'success' : colorScheme === 'red' ? 'error' : 'primary'}
    {...props} 
  />
);

export const Progress = ({ value, ...props }) => (
  <LinearProgress variant="determinate" value={value} {...props} />
);

export const Button = ({ children, colorScheme, variant = 'contained', isLoading, ...props }) => (
  <MuiButton 
    variant={variant === 'solid' ? 'contained' : variant}
    color={colorScheme === 'blue' ? 'primary' : colorScheme === 'green' ? 'success' : 'primary'}
    disabled={isLoading}
    {...props}
  >
    {isLoading ? 'Loading...' : children}
  </MuiButton>
);

export const Flex = ({ children, ...props }) => (
  <MuiBox display="flex" {...props}>
    {children}
  </MuiBox>
);

export const Avatar = styled(MuiAvatar)({});

export const Stat = ({ children, ...props }) => (
  <Paper variant="outlined" sx={{ p: 2 }} {...props}>
    {children}
  </Paper>
);

export const StatLabel = ({ children, ...props }) => (
  <Typography variant="caption" color="textSecondary" {...props}>
    {children}
  </Typography>
);

export const StatNumber = ({ children, ...props }) => (
  <Typography variant="h4" fontWeight="bold" {...props}>
    {children}
  </Typography>
);

export const StatHelpText = ({ children, ...props }) => (
  <Typography variant="body2" color="textSecondary" {...props}>
    {children}
  </Typography>
);

export const StatArrow = ({ type, ...props }) => (
  <Typography component="span" color={type === 'increase' ? 'success.main' : 'error.main'} {...props}>
    {type === 'increase' ? '↗' : '↘'}
  </Typography>
);

export const Spacer = () => <MuiBox flexGrow={1} />;

export const Divider = styled(MuiDivider)({});

export const Input = ({ placeholder, ...props }) => (
  <TextField 
    placeholder={placeholder}
    variant="outlined"
    fullWidth
    {...props}
  />
);

export const Textarea = ({ placeholder, ...props }) => (
  <TextField 
    placeholder={placeholder}
    variant="outlined"
    fullWidth
    multiline
    rows={4}
    {...props}
  />
);

export const Select = ({ children, placeholder, ...props }) => (
  <FormControl fullWidth>
    {placeholder && <InputLabel>{placeholder}</InputLabel>}
    <MuiSelect {...props}>
      {children}
    </MuiSelect>
  </FormControl>
);

export const FormLabel = ({ children, ...props }) => (
  <Typography variant="subtitle2" component="label" {...props}>
    {children}
  </Typography>
);

export const Switch = styled(MuiSwitch)({});
export const Checkbox = styled(MuiCheckbox)({});

export const Tabs = styled(MuiTabs)({});
export const Tab = styled(MuiTab)({});
export const TabList = ({ children, ...props }) => (
  <MuiTabs {...props}>
    {children}
  </MuiTabs>
);
export const TabPanels = ({ children, ...props }) => (
  <MuiBox {...props}>
    {children}
  </MuiBox>
);
export const TabPanel = ({ children, ...props }) => (
  <MuiBox role="tabpanel" {...props}>
    {children}
  </MuiBox>
);

export const Modal = ({ isOpen, onClose, children, ...props }) => (
  <Dialog open={isOpen} onClose={onClose} {...props}>
    {children}
  </Dialog>
);

export const ModalOverlay = () => null; // Not needed in Material-UI
export const ModalContent = ({ children, ...props }) => (
  <MuiBox {...props}>
    {children}
  </MuiBox>
);
export const ModalHeader = ({ children, ...props }) => (
  <DialogTitle {...props}>
    {children}
  </DialogTitle>
);
export const ModalBody = ({ children, ...props }) => (
  <DialogContent {...props}>
    {children}
  </DialogContent>
);
export const ModalFooter = ({ children, ...props }) => (
  <DialogActions {...props}>
    {children}
  </DialogActions>
);

export const IconButton = styled(MuiIconButton)({});

export const Accordion = styled(MuiAccordion)({});
export const AccordionItem = ({ children, ...props }) => (
  <MuiAccordion {...props}>
    {children}
  </MuiAccordion>
);
export const AccordionButton = ({ children, ...props }) => (
  <AccordionSummary {...props}>
    {children}
  </AccordionSummary>
);
export const AccordionPanel = ({ children, ...props }) => (
  <AccordionDetails {...props}>
    {children}
  </AccordionDetails>
);
export const AccordionIcon = () => null; // Use Material-UI's built-in expand icon

export const useColorModeValue = (light, dark) => light; // Always return light mode for now

export const useDisclosure = () => ({
  isOpen: false,
  onOpen: () => {},
  onClose: () => {}
});

// Grid components
export const Grid = ({ children, templateColumns, gap, ...props }) => (
  <Grid container spacing={gap || 2} {...props}>
    {children}
  </Grid>
);

export const GridItem = ({ children, colSpan = 1, ...props }) => (
  <Grid item xs={colSpan} {...props}>
    {children}
  </Grid>
);

// List components
export const List = ({ children, ...props }) => (
  <MuiBox component="ul" {...props}>
    {children}
  </MuiBox>
);

export const ListItem = ({ children, ...props }) => (
  <MuiBox component="li" {...props}>
    {children}
  </MuiBox>
);

export const ListIcon = ({ children, ...props }) => (
  <MuiBox component="span" {...props}>
    {children}
  </MuiBox>
);

// Avatar Group
export const AvatarGroup = ({ children, max = 3, ...props }) => (
  <Stack direction="row" spacing={-1} {...props}>
    {React.Children.toArray(children).slice(0, max)}
    {React.Children.count(children) > max && (
      <Avatar>+{React.Children.count(children) - max}</Avatar>
    )}
  </Stack>
);

// Drawer components (simplified)
export const Drawer = Modal;
export const DrawerOverlay = ModalOverlay;
export const DrawerContent = ModalContent;
export const DrawerCloseButton = ({ onClick }) => (
  <IconButton onClick={onClick}>×</IconButton>
);
export const DrawerHeader = ModalHeader;
export const DrawerBody = ModalBody;

// Menu components (simplified)
export const Menu = ({ children, ...props }) => (
  <MuiBox {...props}>
    {children}
  </MuiBox>
);
export const MenuButton = Button;
export const MenuList = ({ children, ...props }) => (
  <Paper {...props}>
    {children}
  </Paper>
);
export const MenuItem = ({ children, ...props }) => (
  <MuiBox sx={{ p: 1, cursor: 'pointer', '&:hover': { bgcolor: 'grey.100' } }} {...props}>
    {children}
  </MuiBox>
);

// Spinner
export const Spinner = () => <LinearProgress />;

// Collapse
export const Collapse = ({ in: isOpen, children, ...props }) => (
  <MuiBox 
    sx={{ 
      display: isOpen ? 'block' : 'none',
      transition: 'all 0.2s ease-in-out'
    }} 
    {...props}
  >
    {children}
  </MuiBox>
);

export default {
  Box, Container, Heading, Text, VStack, HStack, SimpleGrid,
  Card, CardBody, CardHeader, Badge, Progress, Button, Flex,
  Avatar, Stat, StatLabel, StatNumber, StatHelpText, StatArrow,
  Spacer, Divider, Input, Textarea, Select, FormLabel,
  Switch, Checkbox, Tabs, Tab, TabList, TabPanels, TabPanel,
  Modal, ModalOverlay, ModalContent, ModalHeader, ModalBody, ModalFooter,
  IconButton, Accordion, AccordionItem, AccordionButton, AccordionPanel,
  useColorModeValue, useDisclosure, Grid, GridItem,
  List, ListItem, ListIcon, AvatarGroup
};
