import React from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Button, 
  Stack, 
  Paper,
  Grid,
  useTheme,
  alpha
} from '@mui/material';
import { 
  SmartToy, 
  Dashboard, 
  ArrowForward,
  TrendingUp,
  Group,
  Event,
  Assignment
} from '@mui/icons-material';
import { 
  <PERSON><PERSON>ox, 
  AnimatedButton, 
  StaggerContainer, 
  PageTransition 
} from './AnimatedComponents';

const ModernHero = ({ user, onGetStarted, onNavigate, stats }) => {
  const theme = useTheme();

  return (
    <PageTransition direction="up">
      <Box sx={{ 
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        minHeight: '100vh',
        py: 4,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',
          pointerEvents: 'none'
        }
      }}>
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          {/* Hero Section */}
          <AnimatedBox animation="fadeInUp" duration={1}>
            <Paper 
              elevation={0} 
              sx={{ 
                background: alpha(theme.palette.background.paper, 0.95),
                backdropFilter: 'blur(20px)',
                borderRadius: 4,
                p: { xs: 4, md: 6 },
                mb: 6,
                textAlign: 'center',
                position: 'relative',
                overflow: 'hidden',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main}, ${theme.palette.primary.main})`,
                  backgroundSize: '200% 100%',
                  animation: 'shimmer 3s ease-in-out infinite',
                },
                '@keyframes shimmer': {
                  '0%': { backgroundPosition: '-200% 0' },
                  '100%': { backgroundPosition: '200% 0' }
                }
              }}
            >
              <AnimatedBox animation="scaleIn" delay={0.3}>
                <Typography 
                  variant="h2" 
                  component="h1" 
                  gutterBottom
                  sx={{ 
                    fontWeight: 700,
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 2,
                    fontSize: { xs: '2rem', md: '3rem' }
                  }}
                >
                  🎓 FPT UniHub
                </Typography>
              </AnimatedBox>
              
              <AnimatedBox animation="fadeInUp" delay={0.5}>
                <Typography 
                  variant="h5" 
                  color="text.secondary" 
                  paragraph
                  sx={{ 
                    mb: 4, 
                    maxWidth: '800px', 
                    mx: 'auto',
                    fontSize: { xs: '1.1rem', md: '1.5rem' }
                  }}
                >
                  Nền tảng học tập thông minh với AI, kết nối sinh viên FPT University
                  và nâng cao trải nghiệm học tập của bạn.
                </Typography>
              </AnimatedBox>

              <AnimatedBox animation="slideInLeft" delay={0.7}>
                <Stack 
                  direction={{ xs: 'column', sm: 'row' }} 
                  spacing={2} 
                  justifyContent="center"
                  sx={{ mb: 4 }}
                >
                  <AnimatedButton
                    variant="contained"
                    size="large"
                    startIcon={<SmartToy />}
                    onClick={() => onNavigate('/chatbot')}
                    sx={{
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      px: 4,
                      py: 1.5,
                      borderRadius: 3,
                      position: 'relative',
                      overflow: 'hidden',
                      boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: '-100%',
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                        transition: 'left 0.5s',
                      },
                      '&:hover::before': {
                        left: '100%',
                      },
                      '&:hover': {
                        boxShadow: `0 8px 30px ${alpha(theme.palette.primary.main, 0.4)}`,
                      }
                    }}
                  >
                    Trải nghiệm AI Chat
                  </AnimatedButton>
                  
                  <AnimatedButton
                    variant="outlined"
                    size="large"
                    startIcon={<Dashboard />}
                    onClick={onGetStarted}
                    sx={{
                      borderColor: theme.palette.primary.main,
                      color: theme.palette.primary.main,
                      px: 4,
                      py: 1.5,
                      borderRadius: 3,
                      borderWidth: 2,
                      '&:hover': {
                        borderColor: theme.palette.primary.dark,
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        borderWidth: 2,
                      }
                    }}
                  >
                    {user ? 'Vào Dashboard' : 'Bắt đầu ngay'}
                  </AnimatedButton>
                </Stack>
              </AnimatedBox>

              {/* Stats */}
              <StaggerContainer staggerDelay={0.2}>
                <Grid container spacing={4} sx={{ mt: 2 }}>
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography 
                        variant="h4" 
                        color="primary" 
                        fontWeight="bold"
                        sx={{ 
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                        }}
                      >
                        1000+
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Sinh viên đang sử dụng
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography 
                        variant="h4" 
                        color="primary" 
                        fontWeight="bold"
                        sx={{ 
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                        }}
                      >
                        50+
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Tính năng hữu ích
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography 
                        variant="h4" 
                        color="primary" 
                        fontWeight="bold"
                        sx={{ 
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                        }}
                      >
                        24/7
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Hỗ trợ AI thông minh
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography 
                        variant="h4" 
                        color="primary" 
                        fontWeight="bold"
                        sx={{ 
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                        }}
                      >
                        99%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Độ hài lòng
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </StaggerContainer>
            </Paper>
          </AnimatedBox>
        </Container>
      </Box>
    </PageTransition>
  );
};

export default ModernHero;
