const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    select: false // Don't include password in queries by default
  },
  googleId: {
    type: String,
    sparse: true // Allow multiple null values but unique non-null values
  },
  avatar: {
    type: String,
    default: ''
  },
  role: {
    type: String,
    enum: ['student', 'teacher', 'admin'],
    default: 'student',
    index: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'banned', 'pending'],
    default: 'active',
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  lastLogin: {
    type: Date,
    index: true
  },
  loginAttempts: {
    type: Number,
    default: 0,
    max: 10
  },
  lockUntil: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  todo: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Todo'
  }],
  groups: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group'
  }],
  learningProgress: {
    type: Object,
    default: {}
  },
  points: {
    type: Number,
    default: 0,
    min: 0
  },
  badges: [{
    type: String
  }],
}, {
  timestamps: true // Automatically manage createdAt and updatedAt
});

// Enhanced indexes for better performance (email and googleId already have unique in schema)
userSchema.index({ role: 1, status: 1 });
userSchema.index({ points: -1 }); // For leaderboard queries
userSchema.index({ isActive: 1, status: 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ lastLogin: -1 });
// Compound index for common queries
userSchema.index({ status: 1, isActive: 1, role: 1 });
userSchema.index({ points: -1, role: 1 }); // For role-based leaderboards

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Update lastLogin when user logs in
userSchema.methods.updateLastLogin = function() {
  this.lastLogin = Date.now();
  return this.save();
};

// Method to add points
userSchema.methods.addPoints = function(points) {
  this.points += points;
  return this.save();
};

// Method to add badge
userSchema.methods.addBadge = function(badge) {
  if (!this.badges.includes(badge)) {
    this.badges.push(badge);
    return this.save();
  }
  return Promise.resolve(this);
};

// Account locking methods for security
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }

  const updates = { $inc: { loginAttempts: 1 } };

  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.lockUntil) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }

  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Virtual for checking if account is locked
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Static methods for common queries
userSchema.statics.findActiveUsers = function() {
  return this.find({ status: 'active', isActive: true });
};

userSchema.statics.getLeaderboard = function(limit = 10, role = null) {
  const query = { status: 'active', isActive: true };
  if (role) query.role = role;

  return this.find(query)
    .select('name email points badges role')
    .sort({ points: -1 })
    .limit(limit)
    .lean();
};

// Ensure virtual fields are serialized
userSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.password;
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('User', userSchema);