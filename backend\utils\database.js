const mongoose = require('mongoose');

/**
 * Enhanced Database Connection Manager
 * Handles MongoDB connection with optimization, monitoring, and error handling
 */
class DatabaseManager {
  constructor() {
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5 seconds
    this.healthCheckInterval = null;
    this.connectionMetrics = {
      connectTime: null,
      lastError: null,
      reconnectCount: 0,
      queryCount: 0,
      slowQueries: []
    };
  }

  /**
   * Connect to MongoDB with enhanced configuration
   */
  async connect(mongoURI, options = {}) {
    if (!mongoURI) {
      throw new Error('MongoDB URI is required');
    }

    const defaultOptions = {
      // Connection pool settings
      maxPoolSize: 10, // Maximum number of connections
      minPoolSize: 2,  // Minimum number of connections
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
      
      // Timeout settings
      serverSelectionTimeoutMS: 5000, // How long to try selecting a server
      socketTimeoutMS: 45000, // How long a send or receive on a socket can take
      connectTimeoutMS: 10000, // How long to wait for initial connection
      
      // Heartbeat settings
      heartbeatFrequencyMS: 10000, // How often to check server status
      
      // Buffer settings (removed deprecated options)
      // bufferMaxEntries and bufferCommands are deprecated in newer versions
      
      // Other settings
      retryWrites: true,
      retryReads: true,
      readPreference: 'primary',
      writeConcern: {
        w: 'majority',
        j: true, // Wait for journal
        wtimeout: 5000
      },
      
      ...options
    };

    try {
      console.log('🔄 Connecting to MongoDB...');
      const startTime = Date.now();
      
      await mongoose.connect(mongoURI, defaultOptions);
      
      this.connectionMetrics.connectTime = Date.now() - startTime;
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      console.log(`✅ MongoDB connected successfully in ${this.connectionMetrics.connectTime}ms`);
      console.log(`📊 Connection pool: ${defaultOptions.maxPoolSize} max, ${defaultOptions.minPoolSize} min`);
      
      this.setupEventListeners();
      this.startHealthCheck();
      this.setupQueryMonitoring();
      
      return mongoose.connection;
    } catch (error) {
      this.connectionMetrics.lastError = error;
      this.handleConnectionError(error, mongoURI, defaultOptions);
      throw error;
    }
  }

  /**
   * Setup MongoDB event listeners
   */
  setupEventListeners() {
    const connection = mongoose.connection;

    connection.on('connected', () => {
      console.log('📡 MongoDB connected');
      this.isConnected = true;
    });

    connection.on('error', (error) => {
      console.error('❌ MongoDB connection error:', error);
      this.connectionMetrics.lastError = error;
      this.isConnected = false;
    });

    connection.on('disconnected', () => {
      console.log('📡 MongoDB disconnected');
      this.isConnected = false;
      this.connectionMetrics.reconnectCount++;
      
      // Attempt to reconnect
      if (this.connectionAttempts < this.maxRetries) {
        setTimeout(() => {
          console.log('🔄 Attempting to reconnect...');
          this.connectionAttempts++;
        }, this.retryDelay);
      }
    });

    connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconnected');
      this.isConnected = true;
      this.connectionAttempts = 0;
    });

    // Handle process termination
    process.on('SIGINT', () => {
      this.gracefulShutdown('SIGINT');
    });

    process.on('SIGTERM', () => {
      this.gracefulShutdown('SIGTERM');
    });
  }

  /**
   * Handle connection errors with retry logic
   */
  handleConnectionError(error, mongoURI, options) {
    console.error(`❌ MongoDB connection failed (attempt ${this.connectionAttempts + 1}/${this.maxRetries}):`, error.message);
    
    this.connectionAttempts++;
    
    if (this.connectionAttempts < this.maxRetries) {
      console.log(`⏳ Retrying connection in ${this.retryDelay / 1000} seconds...`);
      setTimeout(() => {
        this.connect(mongoURI, options);
      }, this.retryDelay);
    } else {
      console.error('💀 Max connection attempts reached. Exiting...');
      process.exit(1);
    }
  }

  /**
   * Start health check monitoring
   */
  startHealthCheck() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        await mongoose.connection.db.admin().ping();
        // console.log('💓 Database health check: OK');
      } catch (error) {
        console.error('💔 Database health check failed:', error.message);
        this.isConnected = false;
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Setup query monitoring for performance
   */
  setupQueryMonitoring() {
    // Monitor slow queries
    mongoose.set('debug', (collectionName, method, query, doc, options) => {
      const startTime = Date.now();
      
      // Log slow queries (> 100ms)
      setTimeout(() => {
        const duration = Date.now() - startTime;
        if (duration > 100) {
          const slowQuery = {
            collection: collectionName,
            method,
            query: JSON.stringify(query),
            duration,
            timestamp: new Date()
          };
          
          this.connectionMetrics.slowQueries.push(slowQuery);
          
          // Keep only last 50 slow queries
          if (this.connectionMetrics.slowQueries.length > 50) {
            this.connectionMetrics.slowQueries.shift();
          }
          
          console.warn(`🐌 Slow query detected (${duration}ms):`, {
            collection: collectionName,
            method,
            query: JSON.stringify(query).substring(0, 100)
          });
        }
        
        this.connectionMetrics.queryCount++;
      }, 0);
    });
  }

  /**
   * Get connection metrics
   */
  getMetrics() {
    return {
      isConnected: this.isConnected,
      connectionTime: this.connectionMetrics.connectTime,
      reconnectCount: this.connectionMetrics.reconnectCount,
      queryCount: this.connectionMetrics.queryCount,
      slowQueriesCount: this.connectionMetrics.slowQueries.length,
      lastError: this.connectionMetrics.lastError?.message,
      poolSize: mongoose.connection?.readyState === 1 ? 
        mongoose.connection.db?.serverConfig?.s?.coreTopology?.s?.pool?.totalConnectionCount : 0
    };
  }

  /**
   * Get slow queries for analysis
   */
  getSlowQueries(limit = 10) {
    return this.connectionMetrics.slowQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }

  /**
   * Graceful shutdown
   */
  async gracefulShutdown(signal) {
    console.log(`\n🛑 Received ${signal}. Gracefully shutting down...`);
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    try {
      await mongoose.connection.close();
      console.log('✅ MongoDB connection closed gracefully');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during graceful shutdown:', error);
      process.exit(1);
    }
  }

  /**
   * Create database indexes for all models
   */
  async createIndexes() {
    try {
      console.log('🔍 Creating database indexes...');
      
      const models = mongoose.modelNames();
      const indexPromises = models.map(async (modelName) => {
        const model = mongoose.model(modelName);
        await model.createIndexes();
        console.log(`✅ Indexes created for ${modelName}`);
      });
      
      await Promise.all(indexPromises);
      console.log('🎯 All database indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating indexes:', error);
      throw error;
    }
  }

  /**
   * Database maintenance tasks
   */
  async runMaintenance() {
    try {
      console.log('🧹 Running database maintenance...');
      
      // Get database stats
      const stats = await mongoose.connection.db.stats();
      console.log('📊 Database stats:', {
        collections: stats.collections,
        dataSize: `${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`,
        indexSize: `${(stats.indexSize / 1024 / 1024).toFixed(2)} MB`,
        totalSize: `${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`
      });
      
      // Create indexes if needed
      await this.createIndexes();
      
      console.log('✅ Database maintenance completed');
    } catch (error) {
      console.error('❌ Database maintenance failed:', error);
      throw error;
    }
  }
}

// Create singleton instance
const dbManager = new DatabaseManager();

module.exports = {
  DatabaseManager,
  dbManager,
  
  // Convenience methods
  connect: (uri, options) => dbManager.connect(uri, options),
  getMetrics: () => dbManager.getMetrics(),
  getSlowQueries: (limit) => dbManager.getSlowQueries(limit),
  runMaintenance: () => dbManager.runMaintenance()
};
