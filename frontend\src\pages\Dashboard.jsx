import React, { useState, useEffect } from 'react';
import {
  Box, Container, Heading, Text, VStack, HStack, SimpleGrid,
  Card, CardBody, CardHeader, Badge, Progress, Button, Flex,
  Avatar, Stat, StatArrow, StatHelpText, StatNumber, StatLabel,
  Spacer, Divider, useColorModeValue
} from '../components/ChakraToMui';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../App';
import { TypewriterEffect } from '../components/TypewriterEffect';
import { SmartLoader } from '../components/LoadingAnimation';
import { useToast } from '../components/ToastProvider';
import * as api from '../api';

export default function Dashboard() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalTodos: 0,
    completedTodos: 0,
    upcomingEvents: 0,
    unreadNotifications: 0,
    groupMessages: 0,
    forumPosts: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [quickActions, setQuickActions] = useState([]);
  const toast = useToast();
  
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, teal.50)',
    'linear(to-br, blue.900, purple.900, teal.900)'
  );

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load various data in parallel
      const [todos, events, notifications] = await Promise.allSettled([
        api.getTodos().catch(() => ({ data: [] })),
        api.getEvents().catch(() => ({ data: [] })),
        api.getNotifications().catch(() => ({ data: [] }))
      ]);

      const todoData = todos.value?.data || [];
      const eventData = events.value?.data || [];
      const notificationData = notifications.value?.data || [];

      setStats({
        totalTodos: todoData.length,
        completedTodos: todoData.filter(t => t.isDone).length,
        upcomingEvents: eventData.filter(e => new Date(e.startTime) > new Date()).length,
        unreadNotifications: notificationData.filter(n => !n.isRead).length,
        groupMessages: Math.floor(Math.random() * 50) + 10, // Mock data
        forumPosts: Math.floor(Math.random() * 20) + 5 // Mock data
      });

      // Generate recent activity
      setRecentActivity([
        { type: 'todo', title: 'Hoàn thành bài tập React', time: '2 giờ trước', icon: '✅' },
        { type: 'event', title: 'Tham gia Workshop AI', time: '5 giờ trước', icon: '📅' },
        { type: 'chat', title: 'Tin nhắn mới từ nhóm CNTT', time: '1 ngày trước', icon: '💬' },
        { type: 'forum', title: 'Đăng bài trong diễn đàn', time: '2 ngày trước', icon: '📝' }
      ]);

      // Generate quick actions based on user data
      setQuickActions([
        { title: 'Tạo Todo mới', description: 'Thêm công việc cần làm', link: '/todo', icon: '📝', color: 'blue' },
        { title: 'Tham gia sự kiện', description: 'Xem sự kiện sắp tới', link: '/event', icon: '📅', color: 'green' },
        { title: 'Chat với AI', description: 'Trò chuyện với AI thông minh', link: '/chatbot', icon: '🤖', color: 'purple' },
        { title: 'Kiểm tra thông báo', description: 'Xem thông báo mới', link: '/notification', icon: '🔔', color: 'orange' }
      ]);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({ 
        title: 'Lỗi tải dữ liệu', 
        description: 'Không thể tải dữ liệu dashboard',
        status: 'error' 
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Container maxW="container.md" py={8}>
        <VStack spacing={6} textAlign="center">
          <Heading>Chào mừng đến với FPT UniHub</Heading>
          <Text>Vui lòng đăng nhập để sử dụng dashboard</Text>
          <Button as={RouterLink} to="/login" colorScheme="teal" size="lg">
            Đăng nhập
          </Button>
        </VStack>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <SmartLoader type="floating" />
      </Container>
    );
  }

  const completionRate = stats.totalTodos > 0 ? (stats.completedTodos / stats.totalTodos) * 100 : 0;

  return (
    <Box bgGradient={bgGradient} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Welcome Header */}
          <Card bg="white" boxShadow="xl">
            <CardBody>
              <Flex align="center" justify="space-between">
                <HStack spacing={4}>
                  <Avatar size="lg" name={user.name} bg="teal.500" />
                  <VStack align="start" spacing={1}>
                    <TypewriterEffect
                      text={`Chào mừng trở lại, ${user.name}!`}
                      fontSize="2xl"
                      fontWeight="bold"
                      speed={80}
                    />
                    <Text color="gray.600">
                      Hôm nay là {new Date().toLocaleDateString('vi-VN', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </Text>
                  </VStack>
                </HStack>
                <Button 
                  as={RouterLink} 
                  to="/chatbot" 
                  colorScheme="purple"
                  size="lg"
                  leftIcon={<Text>🤖</Text>}
                >
                  Chat với AI
                </Button>
              </Flex>
            </CardBody>
          </Card>

          {/* Stats Overview */}
          <SimpleGrid columns={{ base: 2, md: 3, lg: 6 }} spacing={4}>
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Todo</StatLabel>
              <StatNumber color="blue.500">{stats.totalTodos}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                {stats.completedTodos} hoàn thành
              </StatHelpText>
            </Stat>
            
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Sự kiện</StatLabel>
              <StatNumber color="green.500">{stats.upcomingEvents}</StatNumber>
              <StatHelpText>Sắp tới</StatHelpText>
            </Stat>
            
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Thông báo</StatLabel>
              <StatNumber color="orange.500">{stats.unreadNotifications}</StatNumber>
              <StatHelpText>Chưa đọc</StatHelpText>
            </Stat>
            
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Tin nhắn</StatLabel>
              <StatNumber color="purple.500">{stats.groupMessages}</StatNumber>
              <StatHelpText>Nhóm chat</StatHelpText>
            </Stat>
            
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Bài viết</StatLabel>
              <StatNumber color="teal.500">{stats.forumPosts}</StatNumber>
              <StatHelpText>Diễn đàn</StatHelpText>
            </Stat>
            
            <Stat bg="white" p={4} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Hoàn thành</StatLabel>
              <StatNumber color="pink.500">{completionRate.toFixed(0)}%</StatNumber>
              <StatHelpText>Tỷ lệ</StatHelpText>
            </Stat>
          </SimpleGrid>

          {/* Progress Overview */}
          <Card bg="white" boxShadow="xl">
            <CardHeader>
              <Heading size="md">📊 Tiến độ học tập</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                <Box w="full">
                  <Flex justify="space-between" mb={2}>
                    <Text fontWeight="medium">Hoàn thành công việc</Text>
                    <Text fontSize="sm" color="gray.600">
                      {stats.completedTodos}/{stats.totalTodos}
                    </Text>
                  </Flex>
                  <Progress 
                    value={completionRate} 
                    colorScheme="green" 
                    size="lg" 
                    borderRadius="full"
                  />
                </Box>
                
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} w="full">
                  <Box textAlign="center" p={4} bg="blue.50" borderRadius="md">
                    <Text fontSize="2xl" mb={1}>📚</Text>
                    <Text fontWeight="bold">Học tập</Text>
                    <Text fontSize="sm" color="gray.600">Tiến độ tốt</Text>
                  </Box>
                  <Box textAlign="center" p={4} bg="green.50" borderRadius="md">
                    <Text fontSize="2xl" mb={1}>🎯</Text>
                    <Text fontWeight="bold">Mục tiêu</Text>
                    <Text fontSize="sm" color="gray.600">Đang thực hiện</Text>
                  </Box>
                  <Box textAlign="center" p={4} bg="purple.50" borderRadius="md">
                    <Text fontSize="2xl" mb={1}>🏆</Text>
                    <Text fontWeight="bold">Thành tích</Text>
                    <Text fontSize="sm" color="gray.600">Xuất sắc</Text>
                  </Box>
                </SimpleGrid>
              </VStack>
            </CardBody>
          </Card>

          {/* Quick Actions & Recent Activity */}
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
            {/* Quick Actions */}
            <Card bg="white" boxShadow="xl">
              <CardHeader>
                <Heading size="md">⚡ Thao tác nhanh</Heading>
              </CardHeader>
              <CardBody>
                <SimpleGrid columns={2} spacing={3}>
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      as={RouterLink}
                      to={action.link}
                      h="auto"
                      p={4}
                      colorScheme={action.color}
                      variant="outline"
                      flexDirection="column"
                      textAlign="center"
                      _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                      transition="all 0.2s"
                    >
                      <Text fontSize="2xl" mb={2}>{action.icon}</Text>
                      <Text fontWeight="bold" fontSize="sm" mb={1}>
                        {action.title}
                      </Text>
                      <Text fontSize="xs" color="gray.600">
                        {action.description}
                      </Text>
                    </Button>
                  ))}
                </SimpleGrid>
              </CardBody>
            </Card>

            {/* Recent Activity */}
            <Card bg="white" boxShadow="xl">
              <CardHeader>
                <Heading size="md">📈 Hoạt động gần đây</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={3} align="stretch">
                  {recentActivity.map((activity, index) => (
                    <Flex key={index} align="center" p={3} bg="gray.50" borderRadius="md">
                      <Text fontSize="xl" mr={3}>{activity.icon}</Text>
                      <Box flex={1}>
                        <Text fontWeight="medium" fontSize="sm">
                          {activity.title}
                        </Text>
                        <Text fontSize="xs" color="gray.600">
                          {activity.time}
                        </Text>
                      </Box>
                      <Badge 
                        colorScheme={
                          activity.type === 'todo' ? 'blue' :
                          activity.type === 'event' ? 'green' :
                          activity.type === 'chat' ? 'purple' : 'orange'
                        }
                        variant="subtle"
                      >
                        {activity.type}
                      </Badge>
                    </Flex>
                  ))}
                </VStack>
              </CardBody>
            </Card>
          </SimpleGrid>
        </VStack>
      </Container>
    </Box>
  );
}
