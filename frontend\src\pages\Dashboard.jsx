import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Paper,
  Stack
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../App';
import {
  Dashboard as DashboardIcon,
  SmartToy,
  Assignment,
  Event,
  Group,
  Notifications
} from '@mui/icons-material';

export default function Dashboard() {
  const { user } = useAuth();

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 8, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          Chào mừng đến với FPT UniHub
        </Typography>
        <Typography variant="body1" sx={{ mb: 4 }}>
          Vui lòng đăng nhập để sử dụng dashboard
        </Typography>
        <Button
          component={RouterLink}
          to="/login"
          variant="contained"
          size="large"
        >
          <PERSON><PERSON><PERSON> nhập
        </Button>
      </Container>
    );
  }

  const stats = [
    { label: 'Todo', value: 12, color: 'primary.main', icon: <Assignment /> },
    { label: 'Sự kiện', value: 3, color: 'success.main', icon: <Event /> },
    { label: 'Thông báo', value: 5, color: 'warning.main', icon: <Notifications /> },
    { label: 'Nhóm', value: 8, color: 'info.main', icon: <Group /> }
  ];

  const quickActions = [
    { title: 'AI Chat', description: 'Trò chuyện với AI', link: '/chatbot', icon: '🤖', color: 'primary' },
    { title: 'Todo List', description: 'Quản lý công việc', link: '/todo', icon: '📝', color: 'secondary' },
    { title: 'Sự kiện', description: 'Xem sự kiện', link: '/event', icon: '📅', color: 'success' },
    { title: 'Nhóm học tập', description: 'Tham gia nhóm', link: '/group', icon: '👥', color: 'info' }
  ];

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      minHeight: '100vh',
      py: 4
    }}>
      <Container maxWidth="xl">
        <Stack spacing={4}>
          {/* Welcome Header */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Stack direction="row" spacing={3} alignItems="center">
              <Avatar sx={{ width: 64, height: 64, bgcolor: 'primary.main' }}>
                {user.name?.charAt(0) || 'U'}
              </Avatar>
              <Box>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  Chào mừng, {user.name}! 👋
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Hôm nay là một ngày tuyệt vời để học tập và phát triển
                </Typography>
              </Box>
            </Stack>
          </Paper>

          {/* Stats Overview */}
          <Grid container spacing={3}>
            {stats.map((stat, index) => (
              <Grid xs={6} md={3} key={index}>
                <Paper elevation={2} sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
                  <Box sx={{ color: stat.color, mb: 1 }}>
                    {stat.icon}
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color={stat.color}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.label}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>

          {/* Quick Actions */}
          <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
              ⚡ Thao tác nhanh
            </Typography>
            <Grid container spacing={3}>
              {quickActions.map((action, index) => (
                <Grid xs={12} sm={6} md={3} key={index}>
                  <Card 
                    elevation={2}
                    sx={{ 
                      height: '100%',
                      transition: 'transform 0.2s',
                      '&:hover': { transform: 'translateY(-4px)' }
                    }}
                  >
                    <CardContent sx={{ textAlign: 'center', p: 3 }}>
                      <Typography variant="h3" sx={{ mb: 2 }}>
                        {action.icon}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {action.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {action.description}
                      </Typography>
                      <Button
                        component={RouterLink}
                        to={action.link}
                        variant="contained"
                        color={action.color}
                        fullWidth
                        sx={{ borderRadius: 2 }}
                      >
                        Truy cập
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>

          {/* Recent Activity */}
          <Grid container spacing={3}>
            <Grid xs={12} md={6}>
              <Paper elevation={3} sx={{ p: 4, borderRadius: 3, height: '100%' }}>
                <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
                  📈 Hoạt động gần đây
                </Typography>
                <Stack spacing={2}>
                  {[
                    { title: 'Hoàn thành bài tập React', time: '2 giờ trước', icon: '✅' },
                    { title: 'Tham gia Workshop AI', time: '5 giờ trước', icon: '📅' },
                    { title: 'Tin nhắn mới từ nhóm', time: '1 ngày trước', icon: '💬' },
                    { title: 'Đăng bài trong diễn đàn', time: '2 ngày trước', icon: '📝' }
                  ].map((activity, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="h6">{activity.icon}</Typography>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body1" fontWeight="medium">
                          {activity.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {activity.time}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Stack>
              </Paper>
            </Grid>

            <Grid xs={12} md={6}>
              <Paper elevation={3} sx={{ p: 4, borderRadius: 3, height: '100%' }}>
                <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
                  🎯 Mục tiêu tuần này
                </Typography>
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      Hoàn thành Todo (8/12)
                    </Typography>
                    <Box sx={{ 
                      width: '100%', 
                      height: 8, 
                      bgcolor: 'grey.200', 
                      borderRadius: 1,
                      overflow: 'hidden'
                    }}>
                      <Box sx={{ 
                        width: '67%', 
                        height: '100%', 
                        bgcolor: 'primary.main',
                        transition: 'width 0.3s'
                      }} />
                    </Box>
                  </Box>
                  
                  <Box>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      Tham gia sự kiện (2/3)
                    </Typography>
                    <Box sx={{ 
                      width: '100%', 
                      height: 8, 
                      bgcolor: 'grey.200', 
                      borderRadius: 1,
                      overflow: 'hidden'
                    }}>
                      <Box sx={{ 
                        width: '67%', 
                        height: '100%', 
                        bgcolor: 'success.main',
                        transition: 'width 0.3s'
                      }} />
                    </Box>
                  </Box>

                  <Box>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      Hoạt động diễn đàn (5/10)
                    </Typography>
                    <Box sx={{ 
                      width: '100%', 
                      height: 8, 
                      bgcolor: 'grey.200', 
                      borderRadius: 1,
                      overflow: 'hidden'
                    }}>
                      <Box sx={{ 
                        width: '50%', 
                        height: '100%', 
                        bgcolor: 'warning.main',
                        transition: 'width 0.3s'
                      }} />
                    </Box>
                  </Box>
                </Stack>
              </Paper>
            </Grid>
          </Grid>
        </Stack>
      </Container>
    </Box>
  );
}
