import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Stack,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Chip,
  LinearProgress,
  Button,
  Avatar,
  Stat,
  StatArrow,
  StatHelpText,
  StatNumber,
  StatLabel,
  Spacer,
  Divider,
  useColorModeValue
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../App';
import { TypewriterEffect } from '../components/TypewriterEffect';
import { SmartLoader } from '../components/LoadingAnimation';
import { useToast } from '../components/ToastProvider';
import * as api from '../api';

export default function Dashboard() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalTodos: 0,
    completedTodos: 0,
    upcomingEvents: 0,
    unreadNotifications: 0,
    groupMessages: 0,
    forumPosts: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [quickActions, setQuickActions] = useState([]);
  const toast = useToast();
  
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, teal.50)',
    'linear(to-br, blue.900, purple.900, teal.900)'
  );

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load various data in parallel
      const [todos, events, notifications] = await Promise.allSettled([
        api.getTodos().catch(() => ({ data: [] })),
        api.getEvents().catch(() => ({ data: [] })),
        api.getNotifications().catch(() => ({ data: [] }))
      ]);

      const todoData = todos.value?.data || [];
      const eventData = events.value?.data || [];
      const notificationData = notifications.value?.data || [];

      setStats({
        totalTodos: todoData.length,
        completedTodos: todoData.filter(t => t.isDone).length,
        upcomingEvents: eventData.filter(e => new Date(e.startTime) > new Date()).length,
        unreadNotifications: notificationData.filter(n => !n.isRead).length,
        groupMessages: Math.floor(Math.random() * 50) + 10, // Mock data
        forumPosts: Math.floor(Math.random() * 20) + 5 // Mock data
      });

      // Generate recent activity
      setRecentActivity([
        { type: 'todo', title: 'Hoàn thành bài tập React', time: '2 giờ trước', icon: '✅' },
        { type: 'event', title: 'Tham gia Workshop AI', time: '5 giờ trước', icon: '📅' },
        { type: 'chat', title: 'Tin nhắn mới từ nhóm CNTT', time: '1 ngày trước', icon: '💬' },
        { type: 'forum', title: 'Đăng bài trong diễn đàn', time: '2 ngày trước', icon: '📝' }
      ]);

      // Generate quick actions based on user data
      setQuickActions([
        { title: 'Tạo Todo mới', description: 'Thêm công việc cần làm', link: '/todo', icon: '📝', color: 'blue' },
        { title: 'Tham gia sự kiện', description: 'Xem sự kiện sắp tới', link: '/event', icon: '📅', color: 'green' },
        { title: 'Chat với AI', description: 'Trò chuyện với AI thông minh', link: '/chatbot', icon: '🤖', color: 'purple' },
        { title: 'Kiểm tra thông báo', description: 'Xem thông báo mới', link: '/notification', icon: '🔔', color: 'orange' }
      ]);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({ 
        title: 'Lỗi tải dữ liệu', 
        description: 'Không thể tải dữ liệu dashboard',
        status: 'error' 
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Container maxW="container.md" py={8}>
        <Stack direction="column" spacing={6} textAlign="center">
          <Typography variant="h4" component="h1">Chào mừng đến với FPT UniHub</Typography>
          <Typography variant="body1">Vui lòng đăng nhập để sử dụng dashboard</Typography>
          <Button as={RouterLink} to="/login" color="primary" size="lg">
            Đăng nhập
          </Button>
        </Stack>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <SmartLoader type="floating" />
      </Container>
    );
  }

  const completionRate = stats.totalTodos > 0 ? (stats.completedTodos / stats.totalTodos) * 100 : 0;

  return (
    <Box bgGradient={bgGradient} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <Stack direction="column" spacing={8} align="stretch">
          {/* Welcome Header */}
          <Card bg="white" boxShadow="xl">
            <CardContent>
              <Box display="flex" align="center" justify="space-between">
                <Stack direction="row" spacing={4}>
                  <Avatar size="lg" name={user.name} bg="teal.500" />
                  <Stack direction="column" align="start" spacing={1}>
                    <TypewriterEffect
                      text={`Chào mừng trở lại, ${user.name}!`}
                      fontSize="2xl"
                      fontWeight="bold"
                      speed={80}
                    />
                    <Typography variant="body1" color="gray.600">
                      Hôm nay là {new Date().toLocaleDateString('vi-VN', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </Typography>
                  </Stack>
                </Stack>
                <Button 
                  as={RouterLink} 
                  to="/chatbot" 
                  color="primary"
                  size="lg"
                  leftIcon={<Typography variant="body1">🤖</Typography>}
                >
                  Chat với AI
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Stats Overview */}
          <SimpleGrid columns={{ base: 2, md: 3, lg: 6 }} spacing={4}>
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Todo</StatLabel>
              <StatNumber color="blue.500">{stats.totalTodos}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                {stats.completedTodos} hoàn thành
              </StatHelpText>
            </Stat>
            
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Sự kiện</StatLabel>
              <StatNumber color="green.500">{stats.upcomingEvents}</StatNumber>
              <StatHelpText>Sắp tới</StatHelpText>
            </Stat>
            
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Thông báo</StatLabel>
              <StatNumber color="orange.500">{stats.unreadNotifications}</StatNumber>
              <StatHelpText>Chưa đọc</StatHelpText>
            </Stat>
            
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Tin nhắn</StatLabel>
              <StatNumber color="purple.500">{stats.groupMessages}</StatNumber>
              <StatHelpText>Nhóm chat</StatHelpText>
            </Stat>
            
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Bài viết</StatLabel>
              <StatNumber color="teal.500">{stats.forumPosts}</StatNumber>
              <StatHelpText>Diễn đàn</StatHelpText>
            </Stat>
            
            <Stat bg="white" sx={{ p: 4 }} borderRadius="lg" boxShadow="md" textAlign="center">
              <StatLabel>Hoàn thành</StatLabel>
              <StatNumber color="pink.500">{completionRate.toFixed(0)}%</StatNumber>
              <StatHelpText>Tỷ lệ</StatHelpText>
            </Stat>
          </SimpleGrid>

          {/* Progress Overview */}
          <Card bg="white" boxShadow="xl">
            <CardHeader>
              <Typography variant="h4" component="h1" size="md">📊 Tiến độ học tập</Typography>
            </CardHeader>
            <CardContent>
              <Stack direction="column" spacing={4}>
                <Box w="full">
                  <Box display="flex" justify="space-between" sx={{ mb: 2 }}>
                    <Typography variant="body1" fontWeight="medium">Hoàn thành công việc</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">
                      {stats.completedTodos}/{stats.totalTodos}
                    </Typography>
                  </Box>
                  <LinearProgress 
                    value={completionRate} 
                    color="primary" 
                    size="lg" 
                    borderRadius="full"
                  />
                </Box>
                
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} w="full">
                  <Box textAlign="center" sx={{ p: 4 }} bg="blue.50" borderRadius="md">
                    <Typography variant="body1" fontSize="2xl" sx={{ mb: 1 }}>📚</Typography>
                    <Typography variant="body1" fontWeight="bold">Học tập</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">Tiến độ tốt</Typography>
                  </Box>
                  <Box textAlign="center" sx={{ p: 4 }} bg="green.50" borderRadius="md">
                    <Typography variant="body1" fontSize="2xl" sx={{ mb: 1 }}>🎯</Typography>
                    <Typography variant="body1" fontWeight="bold">Mục tiêu</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">Đang thực hiện</Typography>
                  </Box>
                  <Box textAlign="center" sx={{ p: 4 }} bg="purple.50" borderRadius="md">
                    <Typography variant="body1" fontSize="2xl" sx={{ mb: 1 }}>🏆</Typography>
                    <Typography variant="body1" fontWeight="bold">Thành tích</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">Xuất sắc</Typography>
                  </Box>
                </SimpleGrid>
              </Stack>
            </CardContent>
          </Card>

          {/* Quick Actions & Recent Activity */}
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
            {/* Quick Actions */}
            <Card bg="white" boxShadow="xl">
              <CardHeader>
                <Typography variant="h4" component="h1" size="md">⚡ Thao tác nhanh</Typography>
              </CardHeader>
              <CardContent>
                <SimpleGrid columns={2} spacing={3}>
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      as={RouterLink}
                      to={action.link}
                      h="auto"
                      sx={{ p: 4 }}
                      colorScheme={action.color}
                      variant="outline"
                      flexDirection="column"
                      textAlign="center"
                      _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                      transition="all 0.2s"
                    >
                      <Typography variant="body1" fontSize="2xl" sx={{ mb: 2 }}>{action.icon}</Typography>
                      <Typography variant="body1" fontWeight="bold" fontSize="sm" sx={{ mb: 1 }}>
                        {action.title}
                      </Typography>
                      <Typography variant="body1" fontSize="xs" color="gray.600">
                        {action.description}
                      </Typography>
                    </Button>
                  ))}
                </SimpleGrid>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card bg="white" boxShadow="xl">
              <CardHeader>
                <Typography variant="h4" component="h1" size="md">📈 Hoạt động gần đây</Typography>
              </CardHeader>
              <CardContent>
                <Stack direction="column" spacing={3} align="stretch">
                  {recentActivity.map((activity, index) => (
                    <Box display="flex" key={index} align="center" sx={{ p: 3 }} bg="gray.50" borderRadius="md">
                      <Typography variant="body1" fontSize="xl" mr={3}>{activity.icon}</Typography>
                      <Box flex={1}>
                        <Typography variant="body1" fontWeight="medium" fontSize="sm">
                          {activity.title}
                        </Typography>
                        <Typography variant="body1" fontSize="xs" color="gray.600">
                          {activity.time}
                        </Typography>
                      </Box>
                      <Chip 
                        colorScheme={
                          activity.type === 'todo' ? 'blue' :
                          activity.type === 'event' ? 'green' :
                          activity.type === 'chat' ? 'purple' : 'orange'
                        }
                        variant="subtle"
                      >
                        {activity.type}
                      </Chip>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </SimpleGrid>
        </Stack>
      </Container>
    </Box>
  );
}
