import React, { useState, useRef, useEffect } from 'react';
import { Box, Skeleton, IconButton } from '@mui/material';
import { BrokenImage, Refresh } from '@mui/icons-material';
import { useImageOptimization } from '../hooks/usePerformance';

/**
 * Optimized Image Component with lazy loading, error handling, and performance tracking
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  placeholder,
  fallback,
  lazy = true,
  quality = 80,
  format = 'webp',
  sizes,
  srcSet,
  onLoad,
  onError,
  className,
  style,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const [loadStartTime, setLoadStartTime] = useState(null);
  const imgRef = useRef(null);
  const { trackImageLoad } = useImageOptimization();

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before image enters viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Generate optimized src with query parameters
  const getOptimizedSrc = (originalSrc) => {
    if (!originalSrc) return '';
    
    // If it's a data URL or external URL, return as is
    if (originalSrc.startsWith('data:') || originalSrc.startsWith('http')) {
      return originalSrc;
    }

    // Add optimization parameters for internal images
    const params = new URLSearchParams();
    if (width) params.append('w', width);
    if (height) params.append('h', height);
    if (quality !== 80) params.append('q', quality);
    if (format !== 'webp') params.append('f', format);

    const queryString = params.toString();
    return queryString ? `${originalSrc}?${queryString}` : originalSrc;
  };

  // Generate WebP srcSet for better compression
  const generateSrcSet = () => {
    if (srcSet) return srcSet;
    
    if (!src || src.startsWith('data:') || src.startsWith('http')) {
      return undefined;
    }

    // Generate responsive srcSet
    const breakpoints = [480, 768, 1024, 1200, 1920];
    return breakpoints
      .map(bp => `${getOptimizedSrc(src)}&w=${bp} ${bp}w`)
      .join(', ');
  };

  const handleLoad = (event) => {
    setIsLoaded(true);
    setIsError(false);
    
    if (loadStartTime) {
      trackImageLoad(loadStartTime, true);
    }
    
    onLoad?.(event);
  };

  const handleError = (event) => {
    setIsError(true);
    setIsLoaded(false);
    
    if (loadStartTime) {
      trackImageLoad(loadStartTime, false);
    }
    
    onError?.(event);
  };

  const handleRetry = () => {
    setIsError(false);
    setIsLoaded(false);
    setLoadStartTime(Date.now());
    
    // Force reload by changing src
    if (imgRef.current) {
      const currentSrc = imgRef.current.src;
      imgRef.current.src = '';
      imgRef.current.src = currentSrc;
    }
  };

  const handleLoadStart = () => {
    setLoadStartTime(Date.now());
  };

  // Render placeholder while loading
  if (!isInView) {
    return (
      <Box
        ref={imgRef}
        sx={{
          width: width || '100%',
          height: height || 'auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'grey.100',
          ...style
        }}
        className={className}
        {...props}
      >
        {placeholder || (
          <Skeleton
            variant="rectangular"
            width={width || '100%'}
            height={height || 200}
            animation="wave"
          />
        )}
      </Box>
    );
  }

  // Render error state
  if (isError) {
    return (
      <Box
        sx={{
          width: width || '100%',
          height: height || 200,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'grey.100',
          color: 'grey.500',
          border: '1px dashed',
          borderColor: 'grey.300',
          borderRadius: 1,
          ...style
        }}
        className={className}
        {...props}
      >
        {fallback || (
          <>
            <BrokenImage sx={{ fontSize: 48, mb: 1 }} />
            <Box sx={{ fontSize: '0.875rem', mb: 1 }}>
              Failed to load image
            </Box>
            <IconButton
              size="small"
              onClick={handleRetry}
              sx={{ color: 'primary.main' }}
            >
              <Refresh />
            </IconButton>
          </>
        )}
      </Box>
    );
  }

  return (
    <Box
      ref={imgRef}
      sx={{
        position: 'relative',
        width: width || '100%',
        height: height || 'auto',
        ...style
      }}
      className={className}
      {...props}
    >
      {/* Loading placeholder */}
      {!isLoaded && (
        <Skeleton
          variant="rectangular"
          width="100%"
          height={height || 200}
          animation="wave"
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1
          }}
        />
      )}
      
      {/* Actual image */}
      <img
        src={getOptimizedSrc(src)}
        srcSet={generateSrcSet()}
        sizes={sizes || '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        onLoadStart={handleLoadStart}
        loading={lazy ? 'lazy' : 'eager'}
        decoding="async"
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          display: 'block'
        }}
      />
    </Box>
  );
};

// Higher-order component for existing images
export const withImageOptimization = (WrappedComponent) => {
  return React.forwardRef((props, ref) => {
    if (props.src) {
      return <OptimizedImage {...props} ref={ref} />;
    }
    return <WrappedComponent {...props} ref={ref} />;
  });
};

// Preload critical images
export const preloadImage = (src, options = {}) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => resolve(img);
    img.onerror = reject;
    
    // Set attributes
    if (options.crossOrigin) img.crossOrigin = options.crossOrigin;
    if (options.referrerPolicy) img.referrerPolicy = options.referrerPolicy;
    
    img.src = src;
  });
};

// Batch preload multiple images
export const preloadImages = async (srcs, options = {}) => {
  const { concurrent = 3 } = options;
  const results = [];
  
  for (let i = 0; i < srcs.length; i += concurrent) {
    const batch = srcs.slice(i, i + concurrent);
    const batchPromises = batch.map(src => 
      preloadImage(src, options).catch(err => ({ error: err, src }))
    );
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
  }
  
  return results;
};

export default OptimizedImage;
