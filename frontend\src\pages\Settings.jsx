import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>ack,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Switch,
  Select,
  TextField,
  FormControl,
  FormLabel,
  Avatar,
  Chip,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@mui/material';
import { useAuth } from '../App';
import { useToast } from '../components/ToastProvider';
import {
  Divider,
  Spacer,
  useColorModeValue
} from '@mui/material';

export default function Settings() {
  const { user, logout } = useAuth();
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      sms: false,
      marketing: false
    },
    privacy: {
      profileVisible: true,
      activityVisible: false,
      contactInfo: 'friends'
    },
    preferences: {
      language: 'vi',
      timezone: 'Asia/Ho_Chi_Minh',
      theme: 'light',
      autoSave: true
    },
    ai: {
      personality: 'professional',
      mode: 'general',
      voiceEnabled: true,
      contextAware: true,
      autoSuggest: true
    }
  });
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  
  const bgGradient = useColorModeValue(
    'linear(to-br, gray.50, blue.50)',
    'linear(to-br, gray.900, blue.900)'
  );

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Save to localStorage for demo
      localStorage.setItem('userSettings', JSON.stringify(settings));
      
      toast({
        title: 'Đã lưu cài đặt',
        description: 'Cài đặt của bạn đã được cập nhật thành công',
        status: 'success'
      });
    } catch (error) {
      toast({
        title: 'Lỗi lưu cài đặt',
        description: 'Không thể lưu cài đặt. Vui lòng thử lại.',
        status: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const resetSettings = () => {
    setSettings({
      notifications: {
        email: true,
        push: true,
        sms: false,
        marketing: false
      },
      privacy: {
        profileVisible: true,
        activityVisible: false,
        contactInfo: 'friends'
      },
      preferences: {
        language: 'vi',
        timezone: 'Asia/Ho_Chi_Minh',
        theme: 'light',
        autoSave: true
      },
      ai: {
        personality: 'professional',
        mode: 'general',
        voiceEnabled: true,
        contextAware: true,
        autoSuggest: true
      }
    });
    toast({
      title: 'Đã reset cài đặt',
      description: 'Tất cả cài đặt đã được khôi phục về mặc định',
      status: 'info'
    });
  };

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  if (!user) {
    return (
      <Container maxW="container.md" py={8}>
        <Stack direction="column" spacing={6} textAlign="center">
          <Typography variant="h4" component="h1">Cài đặt</Typography>
          <Typography variant="body1">Vui lòng đăng nhập để truy cập cài đặt</Typography>
        </Stack>
      </Container>
    );
  }

  return (
    <Box bgGradient={bgGradient} minH="100vh" py={8}>
      <Container maxW="container.lg">
        <Stack direction="column" spacing={8} align="stretch">
          {/* Header */}
          <Card bg="white" boxShadow="xl">
            <CardContent>
              <Stack direction="row" spacing={4}>
                <Avatar size="lg" name={user.name} bg="teal.500" />
                <Stack direction="column" align="start" spacing={1}>
                  <Typography variant="h4" component="h1" size="lg">Cài đặt tài khoản</Typography>
                  <Typography variant="body1" color="gray.600">Quản lý thông tin và tùy chọn của bạn</Typography>
                </Stack>
                <Spacer />
                <Stack direction="row" spacing={2}>
                  <Button onClick={resetSettings} variant="outline" size="sm">
                    Reset
                  </Button>
                  <Button 
                    onClick={saveSettings} 
                    color="primary" 
                    disabled={loading}
                    size="sm"
                  >
                    Lưu cài đặt
                  </Button>
                </Stack>
              </Stack>
            </CardContent>
          </Card>

          {/* Settings Tabs */}
          <Card bg="white" boxShadow="xl">
            <CardContent>
              <Tabs variant="enclosed">
                <TabList>
                  <Tab>🔔 Thông báo</Tab>
                  <Tab>🔒 Quyền riêng tư</Tab>
                  <Tab>⚙️ Tùy chọn</Tab>
                  <Tab>🤖 AI Settings</Tab>
                </TabList>

                <TabPanels>
                  {/* Notifications Tab */}
                  <TabPanel>
                    <Stack direction="column" spacing={6} align="stretch">
                      <Box>
                        <Typography variant="h4" component="h1" size="md" sx={{ mb: 4 }}>Cài đặt thông báo</Typography>
                        <Stack direction="column" spacing={4} align="stretch">
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Email thông báo
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Nhận thông báo qua email
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.notifications.email}
                              onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Push notifications
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Thông báo đẩy trên trình duyệt
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.notifications.push}
                              onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              SMS thông báo
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Nhận thông báo qua SMS
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.notifications.sms}
                              onChange={(e) => handleSettingChange('notifications', 'sms', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Marketing emails
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Nhận email quảng cáo và khuyến mãi
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.notifications.marketing}
                              onChange={(e) => handleSettingChange('notifications', 'marketing', e.target.checked)}
                            />
                          </FormControl>
                        </Stack>
                      </Box>
                    </Stack>
                  </TabPanel>

                  {/* Privacy Tab */}
                  <TabPanel>
                    <Stack direction="column" spacing={6} align="stretch">
                      <Box>
                        <Typography variant="h4" component="h1" size="md" sx={{ mb: 4 }}>Cài đặt quyền riêng tư</Typography>
                        <Stack direction="column" spacing={4} align="stretch">
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Hiển thị hồ sơ công khai
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Cho phép người khác xem hồ sơ của bạn
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.privacy.profileVisible}
                              onChange={(e) => handleSettingChange('privacy', 'profileVisible', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Hiển thị hoạt động
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Cho phép người khác xem hoạt động của bạn
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.privacy.activityVisible}
                              onChange={(e) => handleSettingChange('privacy', 'activityVisible', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl>
                            <FormLabel>Ai có thể liên hệ với bạn?</FormLabel>
                            <Select 
                              value={settings.privacy.contactInfo}
                              onChange={(e) => handleSettingChange('privacy', 'contactInfo', e.target.value)}
                            >
                              <option value="everyone">Mọi người</option>
                              <option value="friends">Chỉ bạn bè</option>
                              <option value="nobody">Không ai</option>
                            </Select>
                          </FormControl>
                        </Stack>
                      </Box>
                    </Stack>
                  </TabPanel>

                  {/* Preferences Tab */}
                  <TabPanel>
                    <Stack direction="column" spacing={6} align="stretch">
                      <Box>
                        <Typography variant="h4" component="h1" size="md" sx={{ mb: 4 }}>Tùy chọn chung</Typography>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                          <FormControl>
                            <FormLabel>Ngôn ngữ</FormLabel>
                            <Select 
                              value={settings.preferences.language}
                              onChange={(e) => handleSettingChange('preferences', 'language', e.target.value)}
                            >
                              <option value="vi">Tiếng Việt</option>
                              <option value="en">English</option>
                            </Select>
                          </FormControl>
                          
                          <FormControl>
                            <FormLabel>Múi giờ</FormLabel>
                            <Select 
                              value={settings.preferences.timezone}
                              onChange={(e) => handleSettingChange('preferences', 'timezone', e.target.value)}
                            >
                              <option value="Asia/Ho_Chi_Minh">Việt Nam (GMT+7)</option>
                              <option value="Asia/Tokyo">Tokyo (GMT+9)</option>
                              <option value="America/New_York">New York (GMT-5)</option>
                            </Select>
                          </FormControl>
                          
                          <FormControl>
                            <FormLabel>Giao diện</FormLabel>
                            <Select 
                              value={settings.preferences.theme}
                              onChange={(e) => handleSettingChange('preferences', 'theme', e.target.value)}
                            >
                              <option value="light">Sáng</option>
                              <option value="dark">Tối</option>
                              <option value="auto">Tự động</option>
                            </Select>
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Tự động lưu
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Tự động lưu thay đổi
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.preferences.autoSave}
                              onChange={(e) => handleSettingChange('preferences', 'autoSave', e.target.checked)}
                            />
                          </FormControl>
                        </SimpleGrid>
                      </Box>
                    </Stack>
                  </TabPanel>

                  {/* AI Settings Tab */}
                  <TabPanel>
                    <Stack direction="column" spacing={6} align="stretch">
                      <Box>
                        <Typography variant="h4" component="h1" size="md" sx={{ mb: 4 }}>Cài đặt AI Assistant</Typography>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                          <FormControl>
                            <FormLabel>Tính cách AI</FormLabel>
                            <Select 
                              value={settings.ai.personality}
                              onChange={(e) => handleSettingChange('ai', 'personality', e.target.value)}
                            >
                              <option value="professional">Chuyên nghiệp</option>
                              <option value="friendly">Thân thiện</option>
                              <option value="expert">Chuyên gia</option>
                              <option value="casual">Thoải mái</option>
                            </Select>
                          </FormControl>
                          
                          <FormControl>
                            <FormLabel>Chế độ mặc định</FormLabel>
                            <Select 
                              value={settings.ai.mode}
                              onChange={(e) => handleSettingChange('ai', 'mode', e.target.value)}
                            >
                              <option value="general">Tổng quát</option>
                              <option value="code">Lập trình</option>
                              <option value="analysis">Phân tích</option>
                              <option value="creative">Sáng tạo</option>
                              <option value="academic">Học thuật</option>
                            </Select>
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Kích hoạt giọng nói
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Sử dụng voice input/output
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.ai.voiceEnabled}
                              onChange={(e) => handleSettingChange('ai', 'voiceEnabled', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Nhận biết ngữ cảnh
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                AI nhớ cuộc trò chuyện trước
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.ai.contextAware}
                              onChange={(e) => handleSettingChange('ai', 'contextAware', e.target.checked)}
                            />
                          </FormControl>
                          
                          <FormControl display="flex" alignItems="center">
                            <FormLabel mb="0" flex="1">
                              Gợi ý tự động
                              <Typography variant="body1" fontSize="sm" color="gray.500">
                                Hiển thị gợi ý thông minh
                              </Typography>
                            </FormLabel>
                            <Switch 
                              isChecked={settings.ai.autoSuggest}
                              onChange={(e) => handleSettingChange('ai', 'autoSuggest', e.target.checked)}
                            />
                          </FormControl>
                        </SimpleGrid>
                      </Box>
                    </Stack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card bg="red.50" borderColor="red.200" borderWidth="1px">
            <CardHeader>
              <Typography variant="h4" component="h1" size="md" color="red.600">⚠️ Vùng nguy hiểm</Typography>
            </CardHeader>
            <CardContent>
              <Stack direction="column" spacing={4} align="stretch">
                <Typography variant="body1" color="red.600">
                  Các hành động này không thể hoàn tác. Vui lòng cân nhắc kỹ trước khi thực hiện.
                </Typography>
                <Stack direction="row" spacing={4}>
                  <Button color="primary" variant="outline" size="sm">
                    Xóa tất cả dữ liệu
                  </Button>
                  <Button color="primary" variant="outline" size="sm" onClick={logout}>
                    Đăng xuất tất cả thiết bị
                  </Button>
                  <Button color="primary" size="sm">
                    Xóa tài khoản
                  </Button>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </Box>
  );
}
