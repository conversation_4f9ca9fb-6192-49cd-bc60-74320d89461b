import React, { useEffect, useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Spinner,
  Typo<PERSON>,
  Chip,
  IconButton
} from '@mui/material';
import { getNotifications, markNotificationAsRead, markAllNotificationsAsRead, deleteNotification } from '../api';
import { useToast } from '../components/ToastProvider';
import {
  Tag
} from '@mui/material';

export default function Notification() {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const toast = useToast();

  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const res = await getNotifications();
      setNotifications(res.data || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({ title: 'Lỗi tải thông báo', status: 'error' });
      setNotifications([]);
    }
    setLoading(false);
  };

  useEffect(() => { fetchNotifications(); }, []);

  const handleMarkRead = async (id) => {
    try {
      await markNotificationAsRead(id);
      fetchNotifications();
      toast({ title: 'Đã đánh dấu đã đọc', status: 'success' });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({ title: 'Lỗi thao tác', status: 'error' });
    }
  };

  const handleMarkAllRead = async () => {
    try {
      await markAllNotificationsAsRead();
      fetchNotifications();
      toast({ title: 'Đã đánh dấu tất cả đã đọc', status: 'success' });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast({ title: 'Lỗi thao tác', status: 'error' });
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Xóa thông báo này?')) return;
    try {
      await deleteNotification(id);
      toast({ title: 'Đã xóa thông báo', status: 'success' });
      fetchNotifications();
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast({ title: 'Lỗi xóa', status: 'error' });
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'welcome': return 'green';
      case 'event': return 'blue';
      case 'exam': return 'red';
      case 'reminder': return 'orange';
      case 'achievement': return 'purple';
      default: return 'gray';
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Box sx={{ p: 4 }} maxW="800px" mx="auto">
      <Stack direction="row" justify="space-between" sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" size="lg">Thông báo</Typography>
      </Stack>
      {loading ? <Spinner /> : (
        <Stack direction="column" spacing={4} align="stretch">
          {notifications.length === 0 && <Box>Chưa có thông báo nào.</Box>}
          {notifications.map(n => (
            <Box key={n._id} sx={{ p: 4 }} borderWidth={1} borderRadius="md" boxShadow="sm" _hover={{ boxShadow: 'md' }} bg={n.read ? 'gray.50' : 'teal.50'}>
              <Stack direction="row" justify="space-between">
                <Box>
                  <Tag colorScheme={n.read ? 'gray' : 'teal'} mr={2}>{n.type || 'Thông báo'}</Tag>
                  {n.content}
                  <Box fontSize="sm" color="gray.500">{new Date(n.createdAt).toLocaleString()}</Box>
                </Box>
                <Stack direction="row">
                  {!n.read && <Button size="sm" onClick={() => handleMarkRead(n._id)}>Đánh dấu đã đọc</Button>}
                  <Button size="sm" color="primary" onClick={() => handleDelete(n._id)}>Xóa</Button>
                </Stack>
              </Stack>
            </Box>
          ))}
        </Stack>
      )}
    </Box>
  );
} 