import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Stack
} from '@mui/material';
import { ArrowForward } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../App';

export default function Home() {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/register');
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      py: 4,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Container maxWidth="md">
        <Paper
          elevation={8}
          sx={{
            p: 6,
            borderRadius: 4,
            textAlign: 'center',
            background: 'rgba(255, 255, 255, 0.95)'
          }}
        >
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 3
            }}
          >
            🎓 FPT UniHub
          </Typography>

          <Typography
            variant="h5"
            color="text.secondary"
            sx={{ mb: 4 }}
          >
            Nền tảng AI toàn diện cho sinh viên FPT University
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}>
            Kết nối, học tập và phát triển cùng cộng đồng FPT với sự hỗ trợ của AI thông minh.
            Quản lý học tập hiệu quả, tham gia hoạt động và xây dựng mạng lưới kết nối.
          </Typography>

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
            <Button
              variant="contained"
              size="large"
              onClick={handleGetStarted}
              startIcon={<ArrowForward />}
              sx={{
                px: 4,
                py: 1.5,
                borderRadius: 3,
                background: 'linear-gradient(45deg, #667eea, #764ba2)'
              }}
            >
              {user ? 'Vào Dashboard' : 'Bắt đầu ngay'}
            </Button>
          </Stack>
        </Paper>
      </Container>
    </Box>
  );
}
