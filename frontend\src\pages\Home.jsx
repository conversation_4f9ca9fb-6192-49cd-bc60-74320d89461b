import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Paper,
  Avatar,
  Chip,
  LinearProgress,
  Stack,
  Divider,
  IconButton,
  Fade,
  Grow,
  Slide,
  Zoom
} from '@mui/material';
// Temporarily comment out FramerAnimations to fix loading issue
// import {
//   AnimatedContainer,
//   AnimatedCard,
//   AnimatedButton,
//   StaggeredContainer,
//   RevealOnScroll,
//   PageTransition,
//   TypewriterText,
//   FloatingElement,
//   fadeInUp,
//   slideInLeft,
//   scaleIn
// } from '../components/FramerAnimations';
import {
  TrendingUp,
  School,
  Group,
  Assignment,
  Chat,
  Event,
  Star,
  ArrowForward,
  SmartToy,
  Dashboard as DashboardIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../App';
import { TypewriterEffect } from '../components/TypewriterEffect';
import { SmartLoader } from '../components/LoadingAnimation';
import * as api from '../api';

export default function Home() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 1250,
    activeEvents: 8,
    completedTasks: 85,
    onlineUsers: 156
  });

  const [features, setFeatures] = useState([
    {
      icon: <SmartToy sx={{ fontSize: 40 }} />,
      title: 'AI Chatbot Thông Minh',
      description: 'Trò chuyện với AI được trang bị Gemini API, hỗ trợ voice, phân tích file và tạo code',
      color: 'primary',
      link: '/chatbot',
      badge: 'HOT'
    },
    {
      icon: <DashboardIcon sx={{ fontSize: 40 }} />,
      title: 'Dashboard Cá Nhân',
      description: 'Theo dõi tiến độ học tập, thống kê và quản lý hoạt động của bạn',
      color: 'secondary',
      link: '/dashboard',
      badge: 'NEW'
    },
    {
      icon: <Group sx={{ fontSize: 40 }} />,
      title: 'Nhóm Học Tập',
      description: 'Tham gia nhóm, chat real-time và chia sẻ tài liệu với bạn bè',
      color: 'success',
      link: '/group'
    },
    {
      icon: <Event sx={{ fontSize: 40 }} />,
      title: 'Sự Kiện & Workshop',
      description: 'Đăng ký tham gia các sự kiện, workshop và hoạt động của trường',
      color: 'warning',
      link: '/event'
    },
    {
      icon: <Assignment sx={{ fontSize: 40 }} />,
      title: 'Quản Lý Công Việc',
      description: 'Todo list thông minh với AI suggestions và deadline tracking',
      color: 'info',
      link: '/todo'
    },
    {
      icon: <Chat sx={{ fontSize: 40 }} />,
      title: 'Diễn Đàn Cộng Đồng',
      description: 'Thảo luận, chia sẻ kiến thức và kết nối với cộng đồng FPT',
      color: 'error',
      link: '/forum'
    }
  ]);

  useEffect(() => {
    // Load initial data
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/register');
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <SmartLoader type="floating" />
      </Container>
    );
  }

  return (
    <Fade in timeout={1000}>
      <Box sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh',
        py: 4,
        position: 'relative',
        overflow: 'hidden'
      }}>
      <Container maxWidth="lg">
        <Stack spacing={6}>
          {/* Hero Section */}
          <Grow in timeout={1000}>
            <Paper
              elevation={8}
              sx={{
                p: 6,
                borderRadius: 4,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                textAlign: 'center',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: 'linear-gradient(90deg, #667eea, #764ba2, #667eea)',
                  backgroundSize: '200% 100%',
                  animation: 'shimmer 3s ease-in-out infinite',
                },
                '@keyframes shimmer': {
                  '0%': { backgroundPosition: '-200% 0' },
                  '100%': { backgroundPosition: '200% 0' }
                }
              }}
            >
              <Zoom in timeout={1500}>
                <Box>
                  <Typography
                    variant="h2"
                    component="h1"
                    gutterBottom
                    sx={{
                      fontWeight: 700,
                      background: 'linear-gradient(45deg, #667eea, #764ba2)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      mb: 3,
                      fontSize: { xs: '2rem', md: '3rem' }
                    }}
                  >
                    🎓 FPT UniHub
                  </Typography>
                </Box>
              </Zoom>
              
              <Slide direction="right" in timeout={2000}>
                <Typography
                  variant="h5"
                  color="text.secondary"
                  sx={{ mb: 4, textAlign: 'center' }}
                >
                  Nền tảng AI toàn diện cho sinh viên FPT University
                </Typography>
              </Slide>

              <Fade in timeout={2500}>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 600, mx: 'auto', textAlign: 'center' }}>
                  Kết nối, học tập và phát triển cùng cộng đồng FPT với sự hỗ trợ của AI thông minh.
                  Quản lý học tập hiệu quả, tham gia hoạt động và xây dựng mạng lưới kết nối.
                </Typography>
              </Fade>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleGetStarted}
                  startIcon={<ArrowForward />}
                  sx={{
                    px: 4,
                    py: 1.5,
                    borderRadius: 3,
                    background: 'linear-gradient(45deg, #667eea, #764ba2)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                      transition: 'left 0.5s',
                    },
                    '&:hover::before': {
                      left: '100%',
                    }
                  }}
                >
                  {user ? 'Vào Dashboard' : 'Bắt đầu ngay'}
                </Button>
                
                <Button
                  variant="outlined"
                  size="large"
                  component={RouterLink}
                  to="/chatbot"
                  startIcon={<SmartToy />}
                  sx={{ px: 4, py: 1.5, borderRadius: 3 }}
                >
                  🤖 Trải nghiệm AI
                </Button>
                
                <Button
                  variant="outlined"
                  size="large"
                  component={RouterLink}
                  to="/features"
                  sx={{ px: 4, py: 1.5, borderRadius: 3 }}
                >
                  ✨ Xem tính năng
                </Button>
              </Stack>
            </Paper>
          </Fade>

          {/* Stats Section */}
          <Grow in timeout={1500}>
            <Grid container spacing={3}>
              {[
                { label: 'Người dùng', value: stats.totalUsers.toLocaleString(), icon: <Group />, color: 'primary' },
                { label: 'Sự kiện đang diễn ra', value: stats.activeEvents, icon: <Event />, color: 'secondary' },
                { label: 'Nhiệm vụ hoàn thành', value: `${stats.completedTasks}%`, icon: <Assignment />, color: 'success' },
                { label: 'Đang online', value: stats.onlineUsers, icon: <TrendingUp />, color: 'warning' }
              ].map((stat, index) => (
                <Grid item xs={6} md={3} key={index}>
                  <Card 
                    elevation={4}
                    sx={{ 
                      p: 2, 
                      textAlign: 'center',
                      borderRadius: 3,
                      transition: 'transform 0.2s',
                      '&:hover': { transform: 'translateY(-4px)' }
                    }}
                  >
                    <Box sx={{ color: `${stat.color}.main`, mb: 1 }}>
                      {stat.icon}
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color={`${stat.color}.main`}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Grow>

          {/* Features Section */}
          <Paper elevation={6} sx={{ p: 4, borderRadius: 4 }}>
            <Typography variant="h4" component="h2" textAlign="center" gutterBottom fontWeight="bold">
              🚀 Tính năng nổi bật
            </Typography>
            <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
              Khám phá các tính năng mạnh mẽ được thiết kế đặc biệt cho sinh viên FPT
            </Typography>

            <Grid container spacing={3}>
              {features.map((feature, index) => (
                <Grid item xs={12} md={6} lg={4} key={index}>
                  <Grow in timeout={1000 + index * 200}>
                    <Card 
                      elevation={3}
                      sx={{ 
                        height: '100%',
                        borderRadius: 3,
                        transition: 'all 0.3s',
                        '&:hover': { 
                          transform: 'translateY(-8px)',
                          boxShadow: 6
                        }
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Stack direction="row" spacing={2} alignItems="flex-start" sx={{ mb: 2 }}>
                          <Box sx={{ color: `${feature.color}.main` }}>
                            {feature.icon}
                          </Box>
                          {feature.badge && (
                            <Chip 
                              label={feature.badge} 
                              size="small" 
                              color={feature.badge === 'HOT' ? 'error' : 'primary'}
                              variant="filled"
                            />
                          )}
                        </Stack>
                        
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {feature.title}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {feature.description}
                        </Typography>
                      </CardContent>
                      
                      <CardActions sx={{ p: 3, pt: 0 }}>
                        <Button
                          component={RouterLink}
                          to={feature.link}
                          variant="outlined"
                          color={feature.color}
                          endIcon={<ArrowForward />}
                          fullWidth
                          sx={{ borderRadius: 2 }}
                        >
                          Khám phá
                        </Button>
                      </CardActions>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Paper>

          {/* CTA Section */}
          <Fade in timeout={2000}>
            <Paper 
              elevation={8}
              sx={{ 
                p: 6, 
                textAlign: 'center',
                borderRadius: 4,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white'
              }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                🎯 Sẵn sàng bắt đầu hành trình học tập?
              </Typography>
              <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                Tham gia cộng đồng FPT UniHub ngay hôm nay!
              </Typography>
              
              {!user && (
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
                  <Button
                    component={RouterLink}
                    to="/register"
                    variant="contained"
                    size="large"
                    sx={{ 
                      px: 4, 
                      py: 1.5,
                      borderRadius: 3,
                      bgcolor: 'white',
                      color: 'primary.main',
                      '&:hover': { bgcolor: 'grey.100' }
                    }}
                  >
                    📝 Đăng ký miễn phí
                  </Button>
                  <Button
                    component={RouterLink}
                    to="/login"
                    variant="outlined"
                    size="large"
                    sx={{ 
                      px: 4, 
                      py: 1.5,
                      borderRadius: 3,
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': { 
                        borderColor: 'white',
                        bgcolor: 'rgba(255,255,255,0.1)'
                      }
                    }}
                  >
                    🔑 Đăng nhập
                  </Button>
                </Stack>
              )}
            </Paper>
          </Fade>
        </Stack>
      </Container>
    </Box>
    </Fade>
  );
}
