import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Stack,
  Divider,
  CircularProgress,
  Avatar,
  Chip
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useToast } from '../components/ToastProvider';

export default function Chatbot() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Xin chào! Tôi là AI Assistant của FPT UniHub. Tôi có thể giúp bạn với các câu hỏi về học tập, l<PERSON><PERSON> tr<PERSON>nh, và nhiều chủ đề khác. Bạn cần hỗ trợ gì?",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim() || loading) return;

    const userMessage = {
      id: Date.now(),
      text: input,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      // Simulate AI response (replace with actual API call)
      setTimeout(() => {
        const botResponse = {
          id: Date.now() + 1,
          text: generateBotResponse(input),
          sender: 'bot',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, botResponse]);
        setLoading(false);
      }, 1000);

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể gửi tin nhắn. Vui lòng thử lại.',
        status: 'error'
      });
      setLoading(false);
    }
  };

  const generateBotResponse = (userInput) => {
    const responses = [
      "Đó là một câu hỏi thú vị! Tôi sẽ cố gắng giúp bạn giải đáp.",
      "Dựa trên những gì bạn hỏi, tôi nghĩ rằng...",
      "Tôi hiểu vấn đề của bạn. Đây là một số gợi ý:",
      "Cảm ơn bạn đã hỏi! Đây là thông tin có thể hữu ích:",
      "Tôi sẽ giúp bạn tìm hiểu về vấn đề này."
    ];
    
    if (userInput.toLowerCase().includes('react')) {
      return "React là một thư viện JavaScript phổ biến để xây dựng giao diện người dùng. Bạn có muốn tôi giải thích thêm về components, hooks, hoặc state management không?";
    }
    
    if (userInput.toLowerCase().includes('javascript')) {
      return "JavaScript là ngôn ngữ lập trình cốt lõi của web development. Bạn cần hỗ trợ về ES6, async/await, hoặc DOM manipulation?";
    }
    
    if (userInput.toLowerCase().includes('học tập') || userInput.toLowerCase().includes('study')) {
      return "Về học tập, tôi khuyên bạn nên tạo lịch học đều đặn, thực hành coding mỗi ngày, và tham gia các dự án thực tế. Bạn có muốn tôi gợi ý một lộ trình học cụ thể không?";
    }
    
    return responses[Math.floor(Math.random() * responses.length)] + " Bạn có thể chia sẻ thêm chi tiết để tôi hỗ trợ tốt hơn không?";
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      py: 4
    }}>
      <Container maxWidth="md">
        <Paper elevation={3} sx={{ height: '80vh', display: 'flex', flexDirection: 'column', borderRadius: 3 }}>
          {/* Header */}
          <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider', bgcolor: 'primary.main', color: 'white', borderRadius: '12px 12px 0 0' }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar sx={{ bgcolor: 'secondary.main' }}>
                <BotIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="bold">
                  AI Assistant
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Trợ lý thông minh của FPT UniHub
                </Typography>
              </Box>
              <Box sx={{ flexGrow: 1 }} />
              <Chip label="Online" color="success" size="small" />
            </Stack>
          </Box>

          {/* Messages */}
          <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
            <Stack spacing={2}>
              {messages.map((message) => (
                <Box
                  key={message.id}
                  sx={{
                    display: 'flex',
                    justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                    mb: 1
                  }}
                >
                  <Stack
                    direction={message.sender === 'user' ? 'row-reverse' : 'row'}
                    spacing={1}
                    alignItems="flex-start"
                    sx={{ maxWidth: '70%' }}
                  >
                    <Avatar sx={{ 
                      bgcolor: message.sender === 'user' ? 'primary.main' : 'secondary.main',
                      width: 32,
                      height: 32
                    }}>
                      {message.sender === 'user' ? <PersonIcon /> : <BotIcon />}
                    </Avatar>
                    <Paper
                      elevation={1}
                      sx={{
                        p: 2,
                        bgcolor: message.sender === 'user' ? 'primary.main' : 'grey.100',
                        color: message.sender === 'user' ? 'white' : 'text.primary',
                        borderRadius: 2,
                        maxWidth: '100%'
                      }}
                    >
                      <Typography variant="body1" sx={{ wordBreak: 'break-word' }}>
                        {message.text}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        sx={{ 
                          opacity: 0.7,
                          display: 'block',
                          mt: 0.5
                        }}
                      >
                        {message.timestamp.toLocaleTimeString()}
                      </Typography>
                    </Paper>
                  </Stack>
                </Box>
              ))}
              
              {loading && (
                <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 1 }}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
                      <BotIcon />
                    </Avatar>
                    <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CircularProgress size={16} />
                        <Typography variant="body2" color="text.secondary">
                          AI đang suy nghĩ...
                        </Typography>
                      </Stack>
                    </Paper>
                  </Stack>
                </Box>
              )}
              <div ref={messagesEndRef} />
            </Stack>
          </Box>

          <Divider />

          {/* Input */}
          <Box sx={{ p: 2 }}>
            <Stack direction="row" spacing={1}>
              <TextField
                fullWidth
                multiline
                maxRows={3}
                placeholder="Nhập tin nhắn của bạn..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={loading}
                variant="outlined"
                size="small"
                sx={{ 
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              />
              <Button
                variant="contained"
                onClick={handleSend}
                disabled={!input.trim() || loading}
                sx={{ 
                  minWidth: 56,
                  height: 56,
                  borderRadius: 2
                }}
              >
                <SendIcon />
              </Button>
            </Stack>
            
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              💡 Mẹo: Nhấn Enter để gửi, Shift+Enter để xuống dòng
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
}
