import React, { useState } from 'react';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../App';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Badge,
  Chip,
  useTheme,
  useMediaQuery,
  Collapse,
  Fade,
  Slide,
  Grow,
  Zoom,
  Tooltip
} from '@mui/material';
import { AnimatedButton, AnimatedBox, StaggerContainer } from '../components/AnimatedComponents';
import {
  Menu as MenuIcon,
  Home,
  Dashboard,
  AutoAwesome,
  LocationOn as GpsIcon,
  SmartToy,
  Event,
  Group,
  Forum,
  EmojiEvents,
  Settings,
  AccountCircle,
  Logout,
  ExpandLess,
  ExpandMore,
  Notifications,
  Psychology
} from '@mui/icons-material';
import { useAuth } from '../App';

export default function Navigation() {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [anchorEl, setAnchorEl] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [expandedMenu, setExpandedMenu] = useState('');

  const mainLinks = [
    { to: '/', label: 'Trang chủ', icon: <Home />, color: 'inherit' },
    { to: '/dashboard', label: 'Dashboard', icon: <Dashboard />, color: 'primary', badge: 'NEW' },
    { to: '/multi-agent', label: 'Multi-Agent AI', icon: <Psychology />, color: 'primary', badge: 'AI' },
    { to: '/features', label: 'Tính năng', icon: <AutoAwesome />, color: 'secondary' },
    { to: '/demo', label: 'Demo', icon: <GpsIcon />, color: 'info' },
    { to: '/chatbot', label: 'AI Chat', icon: <SmartToy />, color: 'success', badge: 'HOT' },
    { to: '/event', label: 'Sự kiện', icon: <Event />, color: 'warning' },
    { to: '/group', label: 'Nhóm', icon: <Group />, color: 'info' },
    { to: '/forum', label: 'Diễn đàn', icon: <Forum />, color: 'secondary' },
    { to: '/leaderboard', label: 'Xếp hạng', icon: <EmojiEvents />, color: 'warning' },
  ];

  const userMenuItems = [
    { label: 'Hồ sơ', icon: <AccountCircle />, action: () => navigate('/profile') },
    { label: 'Cài đặt', icon: <Settings />, action: () => navigate('/settings') },
    { label: 'Thông báo', icon: <Notifications />, action: () => navigate('/notification'), badge: 3 },
  ];

  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
    navigate('/');
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  const drawer = (
    <Box sx={{ width: 280 }}>
      <Box sx={{ p: 2, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h6" fontWeight="bold">
          🎓 FPT UniHub
        </Typography>
        {user && (
          <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
            Xin chào, {user.name}!
          </Typography>
        )}
      </Box>
      
      <List>
        {mainLinks.map((link) => (
          <ListItem key={link.to} disablePadding>
            <ListItemButton
              component={RouterLink}
              to={link.to}
              selected={isActive(link.to)}
              sx={{
                '&.Mui-selected': {
                  bgcolor: `${link.color}.50`,
                  borderRight: 3,
                  borderColor: `${link.color}.main`,
                  '& .MuiListItemIcon-root': {
                    color: `${link.color}.main`,
                  },
                  '& .MuiListItemText-primary': {
                    color: `${link.color}.main`,
                    fontWeight: 'bold',
                  },
                },
                '&:hover': {
                  bgcolor: `${link.color}.50`,
                },
              }}
            >
              <ListItemIcon sx={{ color: link.color === 'inherit' ? 'text.primary' : `${link.color}.main` }}>
                {link.icon}
              </ListItemIcon>
              <ListItemText 
                primary={link.label}
                primaryTypographyProps={{
                  fontWeight: isActive(link.to) ? 'bold' : 'normal'
                }}
              />
              {link.badge && (
                <Chip 
                  label={link.badge} 
                  size="small" 
                  color={link.badge === 'HOT' ? 'error' : 'primary'}
                  variant="filled"
                />
              )}
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider />

      {user && (
        <List>
          {userMenuItems.map((item) => (
            <ListItem key={item.label} disablePadding>
              <ListItemButton onClick={item.action}>
                <ListItemIcon>
                  {item.badge ? (
                    <Badge badgeContent={item.badge} color="error">
                      {item.icon}
                    </Badge>
                  ) : (
                    item.icon
                  )}
                </ListItemIcon>
                <ListItemText primary={item.label} />
              </ListItemButton>
            </ListItem>
          ))}
          
          <ListItem disablePadding>
            <ListItemButton onClick={handleLogout}>
              <ListItemIcon>
                <Logout />
              </ListItemIcon>
              <ListItemText primary="Đăng xuất" />
            </ListItemButton>
          </ListItem>
        </List>
      )}
    </Box>
  );

  return (
    <>
      <AppBar 
        position="fixed" 
        elevation={2}
        sx={{ 
          zIndex: theme.zIndex.drawer + 1,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
        }}
      >
        <Toolbar>
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}

          <Typography 
            variant="h6" 
            component={RouterLink}
            to="/"
            sx={{ 
              flexGrow: 1, 
              textDecoration: 'none', 
              color: 'inherit',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            🎓 FPT UniHub
          </Typography>

          {!isMobile && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {mainLinks.slice(0, 6).map((link) => (
                <Button
                  key={link.to}
                  component={RouterLink}
                  to={link.to}
                  color="inherit"
                  startIcon={link.icon}
                  sx={{
                    position: 'relative',
                    borderRadius: 2,
                    px: 2,
                    py: 1,
                    bgcolor: isActive(link.to) ? 'rgba(255,255,255,0.2)' : 'transparent',
                    '&:hover': {
                      bgcolor: 'rgba(255,255,255,0.1)',
                    },
                    fontWeight: isActive(link.to) ? 'bold' : 'normal',
                  }}
                >
                  {link.label}
                  {link.badge && (
                    <Chip 
                      label={link.badge} 
                      size="small" 
                      color={link.badge === 'HOT' ? 'error' : 'secondary'}
                      variant="filled"
                      sx={{ 
                        position: 'absolute',
                        top: -8,
                        right: -8,
                        fontSize: '0.6rem',
                        height: 16,
                        minWidth: 16,
                      }}
                    />
                  )}
                </Button>
              ))}
            </Box>
          )}

          {user ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton
                color="inherit"
                component={RouterLink}
                to="/notification"
              >
                <Badge badgeContent={3} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
              
              <IconButton
                onClick={handleUserMenuOpen}
                sx={{ p: 0, ml: 1 }}
              >
                <Avatar 
                  sx={{ 
                    bgcolor: 'secondary.main',
                    width: 36,
                    height: 36,
                    fontSize: '0.9rem'
                  }}
                >
                  {user.name?.charAt(0)?.toUpperCase() || 'U'}
                </Avatar>
              </IconButton>
              
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleUserMenuClose}
                onClick={handleUserMenuClose}
                PaperProps={{
                  elevation: 8,
                  sx: {
                    mt: 1.5,
                    minWidth: 200,
                    borderRadius: 2,
                    '& .MuiMenuItem-root': {
                      px: 2,
                      py: 1,
                      borderRadius: 1,
                      mx: 1,
                      my: 0.5,
                    },
                  },
                }}
              >
                <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {user.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user.email}
                  </Typography>
                </Box>
                
                {userMenuItems.map((item) => (
                  <MenuItem key={item.label} onClick={item.action}>
                    <ListItemIcon>
                      {item.badge ? (
                        <Badge badgeContent={item.badge} color="error">
                          {item.icon}
                        </Badge>
                      ) : (
                        item.icon
                      )}
                    </ListItemIcon>
                    {item.label}
                  </MenuItem>
                ))}
                
                <Divider sx={{ my: 1 }} />
                
                <MenuItem onClick={handleLogout}>
                  <ListItemIcon>
                    <Logout fontSize="small" />
                  </ListItemIcon>
                  Đăng xuất
                </MenuItem>
              </Menu>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                component={RouterLink}
                to="/login"
                color="inherit"
                variant="outlined"
                sx={{ 
                  borderColor: 'rgba(255,255,255,0.5)',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255,255,255,0.1)',
                  }
                }}
              >
                Đăng nhập
              </Button>
              <Button
                component={RouterLink}
                to="/register"
                color="inherit"
                variant="contained"
                sx={{ 
                  bgcolor: 'rgba(255,255,255,0.2)',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.3)',
                  }
                }}
              >
                Đăng ký
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: 280,
            borderRadius: '0 16px 16px 0',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Spacer for fixed AppBar */}
      <Toolbar />
    </>
  );
}
