import React, { useState, useEffect, Fragment } from 'react';
import {
  Box,
  Typography,
  Button,
  Stack,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Breadcrumbs,
  Link,
  Chip,
  Avatar,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreIcon,
  Folder as FolderIcon,
  Description as PageIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Home as HomeIcon,
  Share as ShareIcon,
  ContentCopy as DuplicateIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Assignment as TemplateIcon,
  Public as PublicIcon,
  Lock as PrivateIcon
} from '@mui/icons-material';

const PageManager = ({ currentPage, onPageChange, onPageCreate, onPageDelete }) => {
  const [pages, setPages] = useState([
    {
      id: 'home',
      title: '🏠 Home',
      type: 'page',
      parent: null,
      children: ['getting-started', 'templates'],
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'getting-started',
      title: '🚀 Getting Started',
      type: 'page',
      parent: 'home',
      children: [],
      isPublic: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'templates',
      title: '📋 Templates',
      type: 'folder',
      parent: 'home',
      children: ['todo-template', 'project-template'],
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'todo-template',
      title: '✅ Todo Template',
      type: 'template',
      parent: 'templates',
      children: [],
      isPublic: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'project-template',
      title: '🚀 Project Template',
      type: 'template',
      parent: 'templates',
      children: [],
      isPublic: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);

  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedPage, setSelectedPage] = useState(null);
  const [createDialog, setCreateDialog] = useState(false);
  const [shareDialog, setShareDialog] = useState(false);
  const [newPageTitle, setNewPageTitle] = useState('');
  const [newPageType, setNewPageType] = useState('page');
  const [expandedItems, setExpandedItems] = useState(['home', 'templates']);

  const getPageIcon = (page) => {
    switch (page.type) {
      case 'folder': return <FolderIcon />;
      case 'template': return <TemplateIcon />;
      default: return <PageIcon />;
    }
  };

  const getBreadcrumbs = (pageId) => {
    const breadcrumbs = [];
    let current = pages.find(p => p.id === pageId);
    
    while (current) {
      breadcrumbs.unshift(current);
      current = current.parent ? pages.find(p => p.id === current.parent) : null;
    }
    
    return breadcrumbs;
  };

  const handleToggleExpand = (pageId) => {
    setExpandedItems(prev =>
      prev.includes(pageId)
        ? prev.filter(id => id !== pageId)
        : [...prev, pageId]
    );
  };

  const renderListItem = (page, level = 0) => {
    const children = pages.filter(p => p.parent === page.id);
    const isExpanded = expandedItems.includes(page.id);
    const hasChildren = children.length > 0;

    return (
      <Fragment key={page.id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => onPageChange?.(page)}
            selected={currentPage?.id === page.id}
            sx={{
              pl: 2 + level * 2,
              '&.Mui-selected': {
                bgcolor: 'primary.50',
                borderRight: 3,
                borderColor: 'primary.main'
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 32 }}>
              {hasChildren ? (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleExpand(page.id);
                  }}
                >
                  {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                </IconButton>
              ) : (
                getPageIcon(page)
              )}
            </ListItemIcon>

            <ListItemText
              primary={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="body2" sx={{ flexGrow: 1 }}>
                    {page.title}
                  </Typography>
                  {page.isPublic && (
                    <Tooltip title="Public">
                      <PublicIcon fontSize="small" color="success" />
                    </Tooltip>
                  )}
                </Stack>
              }
            />

            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedPage(page);
                setMenuAnchor(e.currentTarget);
              }}
            >
              <MoreIcon fontSize="small" />
            </IconButton>
          </ListItemButton>
        </ListItem>

        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            {children.map(child => renderListItem(child, level + 1))}
          </Collapse>
        )}
      </Fragment>
    );
  };

  const handleCreatePage = () => {
    if (!newPageTitle.trim()) return;

    const newPage = {
      id: Date.now().toString(),
      title: newPageTitle,
      type: newPageType,
      parent: selectedPage?.id || 'home',
      children: [],
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setPages(prev => [...prev, newPage]);
    onPageCreate?.(newPage);
    
    setCreateDialog(false);
    setNewPageTitle('');
    setNewPageType('page');
  };

  const handleDeletePage = (pageId) => {
    setPages(prev => prev.filter(p => p.id !== pageId));
    onPageDelete?.(pageId);
    setMenuAnchor(null);
  };

  const rootPages = pages.filter(p => p.parent === null || p.parent === 'home');

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            📚 Workspace
          </Typography>
          <Button
            startIcon={<AddIcon />}
            variant="contained"
            size="small"
            onClick={() => setCreateDialog(true)}
            sx={{ borderRadius: 2 }}
          >
            New
          </Button>
        </Stack>
      </Box>

      {/* Breadcrumbs */}
      {currentPage && (
        <Box sx={{ p: 2, bgcolor: 'grey.50' }}>
          <Breadcrumbs separator="›" sx={{ fontSize: '0.875rem' }}>
            {getBreadcrumbs(currentPage.id).map((page, index) => (
              <Link
                key={page.id}
                underline="hover"
                color="inherit"
                onClick={() => onPageChange?.(page)}
                sx={{ 
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5
                }}
              >
                {getPageIcon(page)}
                {page.title}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>
      )}

      {/* Page Tree */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List dense>
          {rootPages.map(page => renderListItem(page))}
        </List>
      </Box>

      {/* Quick Actions */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Stack spacing={1}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<TemplateIcon />}
            size="small"
            onClick={() => {
              setNewPageType('template');
              setCreateDialog(true);
            }}
          >
            Create Template
          </Button>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<ShareIcon />}
            size="small"
            onClick={() => setShareDialog(true)}
          >
            Share Workspace
          </Button>
        </Stack>
      </Box>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => onPageChange?.(selectedPage)}>
          <EditIcon sx={{ mr: 1 }} />
          Open
        </MenuItem>
        <MenuItem onClick={() => setCreateDialog(true)}>
          <AddIcon sx={{ mr: 1 }} />
          Add Subpage
        </MenuItem>
        <MenuItem onClick={() => {}}>
          <DuplicateIcon sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        <MenuItem onClick={() => setShareDialog(true)}>
          <ShareIcon sx={{ mr: 1 }} />
          Share
        </MenuItem>
        <Divider />
        <MenuItem 
          onClick={() => handleDeletePage(selectedPage?.id)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create Page Dialog */}
      <Dialog open={createDialog} onClose={() => setCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Page</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              fullWidth
              label="Page Title"
              value={newPageTitle}
              onChange={(e) => setNewPageTitle(e.target.value)}
              placeholder="Enter page title..."
            />
            
            <Stack direction="row" spacing={1}>
              <Chip
                label="📄 Page"
                variant={newPageType === 'page' ? 'filled' : 'outlined'}
                onClick={() => setNewPageType('page')}
                clickable
              />
              <Chip
                label="📁 Folder"
                variant={newPageType === 'folder' ? 'filled' : 'outlined'}
                onClick={() => setNewPageType('folder')}
                clickable
              />
              <Chip
                label="📋 Template"
                variant={newPageType === 'template' ? 'filled' : 'outlined'}
                onClick={() => setNewPageType('template')}
                clickable
              />
            </Stack>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialog(false)}>Cancel</Button>
          <Button onClick={handleCreatePage} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Share Dialog */}
      <Dialog open={shareDialog} onClose={() => setShareDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Share Workspace</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Share this workspace with team members
            </Typography>
            
            <TextField
              fullWidth
              label="Invite by email"
              placeholder="Enter email addresses..."
              helperText="Separate multiple emails with commas"
            />
            
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Current Members
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                <Chip
                  avatar={<Avatar>U</Avatar>}
                  label="You (Owner)"
                  color="primary"
                />
                <Chip
                  avatar={<Avatar>T</Avatar>}
                  label="Team Member"
                  variant="outlined"
                />
              </Stack>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialog(false)}>Close</Button>
          <Button variant="contained">Send Invites</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PageManager;
