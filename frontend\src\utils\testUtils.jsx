import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import theme from '../theme/MuiTheme';

/**
 * Enhanced test utilities for React Testing Library
 */

// Mock implementations
export const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

export const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock API responses
export const mockApiResponse = (data, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data)),
});

// Mock fetch
export const mockFetch = (response) => {
  global.fetch = vi.fn(() => Promise.resolve(mockApiResponse(response)));
};

// Mock socket.io
export const mockSocket = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  connected: true,
  id: 'mock-socket-id',
};

// Test wrapper with providers
const TestWrapper = ({ children, initialEntries = ['/'] }) => {
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Enhanced render function
export const renderWithProviders = (ui, options = {}) => {
  const { initialEntries, ...renderOptions } = options;
  
  const Wrapper = ({ children }) => (
    <TestWrapper initialEntries={initialEntries}>
      {children}
    </TestWrapper>
  );

  return {
    user: userEvent.setup(),
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Custom queries
export const queries = {
  getByTestId: (testId) => screen.getByTestId(testId),
  queryByTestId: (testId) => screen.queryByTestId(testId),
  findByTestId: (testId) => screen.findByTestId(testId),
  
  getByRole: (role, options) => screen.getByRole(role, options),
  queryByRole: (role, options) => screen.queryByRole(role, options),
  findByRole: (role, options) => screen.findByRole(role, options),
  
  getByText: (text, options) => screen.getByText(text, options),
  queryByText: (text, options) => screen.queryByText(text, options),
  findByText: (text, options) => screen.findByText(text, options),
  
  getByLabelText: (text, options) => screen.getByLabelText(text, options),
  queryByLabelText: (text, options) => screen.queryByLabelText(text, options),
  findByLabelText: (text, options) => screen.findByLabelText(text, options),
};

// Custom matchers
export const customMatchers = {
  toBeInTheDocument: (element) => expect(element).toBeInTheDocument(),
  toHaveClass: (element, className) => expect(element).toHaveClass(className),
  toHaveStyle: (element, style) => expect(element).toHaveStyle(style),
  toBeVisible: (element) => expect(element).toBeVisible(),
  toBeDisabled: (element) => expect(element).toBeDisabled(),
  toHaveValue: (element, value) => expect(element).toHaveValue(value),
};

// Test helpers
export const testHelpers = {
  // Wait for element to appear
  waitForElement: async (selector, timeout = 5000) => {
    return waitFor(() => screen.getByTestId(selector), { timeout });
  },

  // Wait for element to disappear
  waitForElementToBeRemoved: async (selector, timeout = 5000) => {
    const element = screen.queryByTestId(selector);
    if (element) {
      return waitFor(() => expect(element).not.toBeInTheDocument(), { timeout });
    }
  },

  // Simulate user typing
  typeInInput: async (input, text) => {
    const user = userEvent.setup();
    await user.clear(input);
    await user.type(input, text);
  },

  // Simulate form submission
  submitForm: async (form) => {
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /submit/i }));
  },

  // Wait for loading to complete
  waitForLoadingToComplete: async () => {
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
  },

  // Check for error messages
  expectErrorMessage: (message) => {
    expect(screen.getByText(message)).toBeInTheDocument();
  },

  // Check for success messages
  expectSuccessMessage: (message) => {
    expect(screen.getByText(message)).toBeInTheDocument();
  },
};

// Mock hooks
export const mockHooks = {
  useAuth: () => ({
    user: { id: '1', name: 'Test User', email: '<EMAIL>' },
    login: vi.fn(),
    logout: vi.fn(),
    loading: false,
  }),

  useSocket: () => ({
    isConnected: true,
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  }),

  useMultiAgent: () => ({
    executeTask: vi.fn(),
    metrics: {
      totalTasks: 10,
      completedTasks: 8,
      failedTasks: 2,
    },
    loading: false,
  }),
};

// Performance testing utilities
export const performanceHelpers = {
  measureRenderTime: (component) => {
    const start = performance.now();
    render(component);
    const end = performance.now();
    return end - start;
  },

  measureAsyncOperation: async (operation) => {
    const start = performance.now();
    await operation();
    const end = performance.now();
    return end - start;
  },

  checkMemoryLeaks: () => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
      };
    }
    return null;
  },
};

// Accessibility testing helpers
export const a11yHelpers = {
  checkAriaLabels: (container) => {
    const elementsWithoutLabels = container.querySelectorAll(
      'input:not([aria-label]):not([aria-labelledby]), button:not([aria-label]):not([aria-labelledby])'
    );
    return elementsWithoutLabels.length === 0;
  },

  checkKeyboardNavigation: async (container) => {
    const user = userEvent.setup();
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    for (let element of focusableElements) {
      await user.tab();
      expect(document.activeElement).toBe(element);
    }
  },

  checkColorContrast: (element) => {
    const styles = window.getComputedStyle(element);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    
    // This is a simplified check - in real scenarios, use a proper contrast checker
    return backgroundColor !== color;
  },
};

// API testing utilities
export const apiTestHelpers = {
  mockSuccessResponse: (data) => {
    mockFetch({ success: true, data });
  },

  mockErrorResponse: (error, status = 400) => {
    global.fetch = vi.fn(() => 
      Promise.resolve(mockApiResponse({ success: false, error }, status))
    );
  },

  mockNetworkError: () => {
    global.fetch = vi.fn(() => Promise.reject(new Error('Network error')));
  },

  expectApiCall: (url, options = {}) => {
    expect(global.fetch).toHaveBeenCalledWith(url, expect.objectContaining(options));
  },
};

// Component testing patterns
export const componentTestPatterns = {
  // Test component rendering
  testRender: (Component, props = {}) => {
    it('renders without crashing', () => {
      renderWithProviders(<Component {...props} />);
    });
  },

  // Test props
  testProps: (Component, requiredProps, optionalProps = {}) => {
    it('handles required props correctly', () => {
      renderWithProviders(<Component {...requiredProps} />);
    });

    it('handles optional props correctly', () => {
      renderWithProviders(<Component {...requiredProps} {...optionalProps} />);
    });
  },

  // Test user interactions
  testInteractions: (Component, props, interactions) => {
    interactions.forEach(({ name, action, expectation }) => {
      it(`handles ${name} correctly`, async () => {
        renderWithProviders(<Component {...props} />);
        await action();
        expectation();
      });
    });
  },

  // Test error states
  testErrorStates: (Component, props, errorScenarios) => {
    errorScenarios.forEach(({ name, setup, expectation }) => {
      it(`handles ${name} error correctly`, async () => {
        setup();
        renderWithProviders(<Component {...props} />);
        await expectation();
      });
    });
  },
};

export {
  render,
  screen,
  fireEvent,
  waitFor,
  userEvent,
  vi,
};

export default {
  renderWithProviders,
  queries,
  customMatchers,
  testHelpers,
  mockHooks,
  performanceHelpers,
  a11yHelpers,
  apiTestHelpers,
  componentTestPatterns,
};
