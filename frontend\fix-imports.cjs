const fs = require('fs');
const path = require('path');

// Files to update
const filesToUpdate = [
  'src/components/AdvancedChatbot.jsx',
  'src/components/TypewriterEffect.jsx',
  'src/pages/CalendarSync.jsx',
  'src/pages/Features.jsx',
  'src/pages/FileManager.jsx',
  'src/pages/Forum.jsx',
  'src/pages/Group.jsx',
  'src/pages/Home_old.jsx',
  'src/pages/Leaderboard.jsx',
  'src/pages/Login_old.jsx',
  'src/pages/Notification.jsx',
  'src/pages/Settings.jsx',
  'src/pages/Sidebar.jsx',
  'src/pages/Summary.jsx',
  'src/pages/ToDo.jsx',
  'src/pages/Dashboard.jsx'
];

// Function to replace ChakraToMui imports with Material-UI
function fixImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace ChakraToMui import with Material-UI import
    content = content.replace(
      /import\s*{([^}]+)}\s*from\s*['"]\.\.\/components\/ChakraToMui['"];?/g,
      (match, imports) => {
        // Map common Chakra components to Material-UI
        const mappings = {
          'Box': 'Box',
          'Container': 'Container',
          'Heading': 'Typography',
          'Text': 'Typography',
          'Button': 'Button',
          'Stack': 'Stack',
          'VStack': 'Stack',
          'HStack': 'Stack',
          'Input': 'TextField',
          'Textarea': 'TextField',
          'Select': 'Select',
          'Card': 'Card',
          'CardBody': 'CardContent',
          'CardHeader': 'CardHeader',
          'Badge': 'Chip',
          'Progress': 'LinearProgress',
          'Avatar': 'Avatar',
          'Flex': 'Box',
          'Grid': 'Grid',
          'GridItem': 'Grid',
          'SimpleGrid': 'Grid',
          'Divider': 'Divider',
          'IconButton': 'IconButton',
          'Paper': 'Paper'
        };
        
        // Extract component names and map them
        const componentNames = imports.split(',').map(name => name.trim());
        const muiComponents = [];
        
        componentNames.forEach(name => {
          if (mappings[name]) {
            if (!muiComponents.includes(mappings[name])) {
              muiComponents.push(mappings[name]);
            }
          } else {
            // Keep unknown components as is
            muiComponents.push(name);
          }
        });
        
        return `import {\n  ${muiComponents.join(',\n  ')}\n} from '@mui/material';`;
      }
    );
    
    // Replace component usage patterns
    content = content.replace(/\<Heading([^>]*)\>/g, '<Typography variant="h4" component="h1"$1>');
    content = content.replace(/\<Text([^>]*)\>/g, '<Typography variant="body1"$1>');
    content = content.replace(/\<VStack([^>]*)\>/g, '<Stack direction="column"$1>');
    content = content.replace(/\<HStack([^>]*)\>/g, '<Stack direction="row"$1>');
    content = content.replace(/\<Input([^>]*)\>/g, '<TextField variant="outlined"$1>');
    content = content.replace(/\<Textarea([^>]*)\>/g, '<TextField multiline rows={4} variant="outlined"$1>');
    content = content.replace(/\<CardBody([^>]*)\>/g, '<CardContent$1>');
    content = content.replace(/\<Badge([^>]*)\>/g, '<Chip$1>');
    content = content.replace(/\<Progress([^>]*)\>/g, '<LinearProgress$1>');
    content = content.replace(/\<Flex([^>]*)\>/g, '<Box display="flex"$1>');
    
    // Replace closing tags
    content = content.replace(/\<\/Heading\>/g, '</Typography>');
    content = content.replace(/\<\/Text\>/g, '</Typography>');
    content = content.replace(/\<\/VStack\>/g, '</Stack>');
    content = content.replace(/\<\/HStack\>/g, '</Stack>');
    content = content.replace(/\<\/Input\>/g, '</TextField>');
    content = content.replace(/\<\/Textarea\>/g, '</TextField>');
    content = content.replace(/\<\/CardBody\>/g, '</CardContent>');
    content = content.replace(/\<\/Badge\>/g, '</Chip>');
    content = content.replace(/\<\/Progress\>/g, '</LinearProgress>');
    content = content.replace(/\<\/Flex\>/g, '</Box>');
    
    // Fix common prop patterns
    content = content.replace(/colorScheme="([^"]+)"/g, 'color="primary"');
    content = content.replace(/isLoading={([^}]+)}/g, 'disabled={$1}');
    content = content.replace(/spacing={([^}]+)}/g, 'spacing={$1}');
    content = content.replace(/mb={([^}]+)}/g, 'sx={{ mb: $1 }}');
    content = content.replace(/mt={([^}]+)}/g, 'sx={{ mt: $1 }}');
    content = content.replace(/p={([^}]+)}/g, 'sx={{ p: $1 }}');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

// Update all files
console.log('🔄 Fixing ChakraToMui imports...\n');

filesToUpdate.forEach(file => {
  const fullPath = path.join(__dirname, file);
  if (fs.existsSync(fullPath)) {
    fixImports(fullPath);
  } else {
    console.log(`⚠️  File not found: ${fullPath}`);
  }
});

console.log('\n✅ All imports have been fixed!');
console.log('🚀 You can now run the frontend without ChakraToMui errors.');
