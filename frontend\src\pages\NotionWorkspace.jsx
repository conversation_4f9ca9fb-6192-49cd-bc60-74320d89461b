import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  <PERSON>ack,
  Paper,
  Drawer,
  AppBar,
  Toolbar,
  IconButton,
  Breadcrumbs,
  Link,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  Tooltip,
  Badge,
  Container
} from '@mui/material';
import {
  Menu as MenuIcon,
  Add as AddIcon,
  Share as ShareIcon,
  MoreVert as MoreIcon,
  Search as SearchIcon,
  Notifications as NotificationIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  ViewKanban as DatabaseIcon,
  Description as PageIcon,
  Assignment as TemplateIcon,
  Comment as CommentIcon,
  History as HistoryIcon,
  Star as StarIcon,
  Public as PublicIcon
} from '@mui/icons-material';
import { useAuth } from '../App';
import PageManager from '../components/PageManager';
import BlockEditor from '../components/BlockEditor';
import DatabaseViews from '../components/DatabaseViews';

const DRAWER_WIDTH = 280;

const NotionWorkspace = () => {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentPage, setCurrentPage] = useState({
    id: 'home',
    title: '🏠 Home',
    type: 'page',
    content: [
      { id: 1, type: 'heading', content: 'Welcome to Your Workspace', metadata: {} },
      { id: 2, type: 'text', content: 'This is your personal Notion-style workspace. Start by creating pages, databases, or templates.', metadata: {} },
      { id: 3, type: 'callout', content: 'Pro tip: Type "/" to add different types of blocks!', metadata: { calloutType: 'INFO' } }
    ]
  });
  const [viewMode, setViewMode] = useState('page'); // page, database
  const [databaseView, setDatabaseView] = useState('table');
  const [shareDialog, setShareDialog] = useState(false);
  const [commentDialog, setCommentDialog] = useState(false);
  const [searchDialog, setSearchDialog] = useState(false);

  // Mock database data
  const [databaseData, setDatabaseData] = useState([
    {
      id: 1,
      title: 'Complete project proposal',
      status: 'in-progress',
      priority: 'high',
      assignee: 'John Doe',
      deadline: '2024-01-15',
      progress: 75,
      emoji: '📝'
    },
    {
      id: 2,
      title: 'Review design mockups',
      status: 'review',
      priority: 'medium',
      assignee: 'Jane Smith',
      deadline: '2024-01-12',
      progress: 90,
      emoji: '🎨'
    },
    {
      id: 3,
      title: 'Setup development environment',
      status: 'completed',
      priority: 'high',
      assignee: 'Mike Johnson',
      deadline: '2024-01-10',
      progress: 100,
      emoji: '⚙️'
    }
  ]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    setViewMode(page.type === 'database' ? 'database' : 'page');
  };

  const handlePageCreate = (newPage) => {
    console.log('Created new page:', newPage);
  };

  const handlePageDelete = (pageId) => {
    console.log('Deleted page:', pageId);
  };

  const handleBlocksChange = (newBlocks) => {
    setCurrentPage(prev => ({
      ...prev,
      content: newBlocks
    }));
  };

  const handleDatabaseItemClick = (item) => {
    console.log('Database item clicked:', item);
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          bgcolor: 'white',
          color: 'text.primary',
          boxShadow: 1
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            📚 StudyFlow Workspace
          </Typography>

          <Stack direction="row" spacing={1}>
            <IconButton onClick={() => setSearchDialog(true)}>
              <SearchIcon />
            </IconButton>
            
            <IconButton>
              <Badge badgeContent={3} color="error">
                <NotificationIcon />
              </Badge>
            </IconButton>

            <Button
              startIcon={<ShareIcon />}
              variant="outlined"
              size="small"
              onClick={() => setShareDialog(true)}
            >
              Share
            </Button>

            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              {user?.name?.charAt(0) || 'U'}
            </Avatar>
          </Stack>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={sidebarOpen}
        sx={{
          width: DRAWER_WIDTH,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: DRAWER_WIDTH,
            boxSizing: 'border-box',
            borderRight: 1,
            borderColor: 'divider'
          },
        }}
      >
        <Toolbar />
        <PageManager
          currentPage={currentPage}
          onPageChange={handlePageChange}
          onPageCreate={handlePageCreate}
          onPageDelete={handlePageDelete}
        />
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          transition: 'margin 0.3s',
          marginLeft: sidebarOpen ? 0 : `-${DRAWER_WIDTH}px`,
          height: '100vh',
          overflow: 'auto'
        }}
      >
        <Toolbar />
        
        <Container maxWidth="lg" sx={{ py: 3 }}>
          {/* Page Header */}
          <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 3 }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Stack direction="row" alignItems="center" spacing={2}>
                <Typography variant="h4" fontWeight="bold">
                  {currentPage.title}
                </Typography>
                
                <Stack direction="row" spacing={1}>
                  {currentPage.isPublic && (
                    <Chip
                      icon={<PublicIcon />}
                      label="Public"
                      size="small"
                      color="success"
                      variant="outlined"
                    />
                  )}
                  
                  <Chip
                    icon={currentPage.type === 'database' ? <DatabaseIcon /> : <PageIcon />}
                    label={currentPage.type === 'database' ? 'Database' : 'Page'}
                    size="small"
                    variant="outlined"
                  />
                </Stack>
              </Stack>

              <Stack direction="row" spacing={1}>
                <IconButton onClick={() => setCommentDialog(true)}>
                  <Badge badgeContent={2} color="primary">
                    <CommentIcon />
                  </Badge>
                </IconButton>
                
                <IconButton>
                  <StarIcon />
                </IconButton>
                
                <IconButton>
                  <HistoryIcon />
                </IconButton>
                
                <IconButton>
                  <MoreIcon />
                </IconButton>
              </Stack>
            </Stack>
          </Paper>

          {/* Content Area */}
          {viewMode === 'database' ? (
            <DatabaseViews
              data={databaseData}
              viewType={databaseView}
              onViewChange={setDatabaseView}
              onItemClick={handleDatabaseItemClick}
            />
          ) : (
            <Paper elevation={1} sx={{ p: 4, borderRadius: 3, minHeight: 600 }}>
              <BlockEditor
                blocks={currentPage.content || []}
                onChange={handleBlocksChange}
                placeholder="Start writing, or type '/' for commands..."
              />
            </Paper>
          )}
        </Container>
      </Box>

      {/* Share Dialog */}
      <Dialog open={shareDialog} onClose={() => setShareDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Share "{currentPage.title}"</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              fullWidth
              label="Invite people"
              placeholder="Enter email addresses..."
              helperText="People with access can view and edit this page"
            />
            
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Share to web
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Button variant="outlined" startIcon={<PublicIcon />}>
                  Publish to web
                </Button>
                <Typography variant="body2" color="text.secondary">
                  Make this page public on the web
                </Typography>
              </Stack>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialog(false)}>Close</Button>
          <Button variant="contained">Share</Button>
        </DialogActions>
      </Dialog>

      {/* Comment Dialog */}
      <Dialog open={commentDialog} onClose={() => setCommentDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Comments</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Stack direction="row" spacing={2}>
                <Avatar sx={{ width: 32, height: 32 }}>J</Avatar>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    John Doe
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Great work on this page! The block structure is very clear.
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    2 hours ago
                  </Typography>
                </Box>
              </Stack>
            </Paper>
            
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Add a comment..."
              variant="outlined"
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCommentDialog(false)}>Close</Button>
          <Button variant="contained">Comment</Button>
        </DialogActions>
      </Dialog>

      {/* Search Dialog */}
      <Dialog open={searchDialog} onClose={() => setSearchDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Search Workspace</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            placeholder="Search pages, databases, and content..."
            variant="outlined"
            sx={{ mt: 1 }}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
          
          <Typography variant="subtitle2" sx={{ mt: 3, mb: 1 }}>
            Recent Pages
          </Typography>
          
          <Stack spacing={1}>
            {['🏠 Home', '📋 Todo Template', '🚀 Project Template'].map((page, index) => (
              <Paper
                key={index}
                variant="outlined"
                sx={{ p: 2, cursor: 'pointer', '&:hover': { bgcolor: 'grey.50' } }}
              >
                <Typography variant="body2">{page}</Typography>
              </Paper>
            ))}
          </Stack>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default NotionWorkspace;
