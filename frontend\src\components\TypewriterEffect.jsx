import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box
} from '@mui/material';

export const TypewriterEffect = ({ 
  text, 
  speed = 50, 
  onComplete, 
  showCursor = true,
  cursorChar = '|',
  startDelay = 0,
  ...textProps 
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [showCursorState, setShowCursorState] = useState(true);

  useEffect(() => {
    if (!text) return;

    const startTimer = setTimeout(() => {
      if (currentIndex < text.length) {
        const timer = setTimeout(() => {
          setDisplayText(text.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        }, speed);

        return () => clearTimeout(timer);
      } else if (!isComplete) {
        setIsComplete(true);
        onComplete && onComplete();
      }
    }, startDelay);

    return () => clearTimeout(startTimer);
  }, [text, currentIndex, speed, onComplete, isComplete, startDelay]);

  // Cursor blinking effect
  useEffect(() => {
    if (!showCursor) return;

    const cursorTimer = setInterval(() => {
      setShowCursorState(prev => !prev);
    }, 500);

    return () => clearInterval(cursorTimer);
  }, [showCursor]);

  // Reset when text changes
  useEffect(() => {
    setDisplayText('');
    setCurrentIndex(0);
    setIsComplete(false);
  }, [text]);

  return (
    <Typography variant="body1" {...textProps}>
      {displayText}
      {showCursor && (showCursorState || !isComplete) && (
        <Box as="span" color="teal.500" fontWeight="bold">
          {cursorChar}
        </Box>
      )}
    </Typography>
  );
};

export const MultilineTypewriter = ({ 
  lines, 
  speed = 50, 
  lineDelay = 1000,
  onComplete,
  ...textProps 
}) => {
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const [completedLines, setCompletedLines] = useState([]);

  const handleLineComplete = () => {
    setCompletedLines(prev => [...prev, lines[currentLineIndex]]);
    
    if (currentLineIndex < lines.length - 1) {
      setTimeout(() => {
        setCurrentLineIndex(currentLineIndex + 1);
      }, lineDelay);
    } else {
      onComplete && onComplete();
    }
  };

  return (
    <Box>
      {completedLines.map((line, index) => (
        <Typography variant="body1" key={index} {...textProps} sx={{ mb: 1 }}>
          {line}
        </Typography>
      ))}
      {currentLineIndex < lines.length && (
        <TypewriterEffect
          text={lines[currentLineIndex]}
          speed={speed}
          onComplete={handleLineComplete}
          {...textProps}
        />
      )}
    </Box>
  );
};

export const CodeTypewriter = ({ 
  code, 
  language = 'javascript',
  speed = 30,
  onComplete,
  ...props 
}) => {
  return (
    <Box
      bg="gray.900"
      color="green.400"
      sx={{ p: 4 }}
      borderRadius="md"
      fontFamily="mono"
      fontSize="sm"
      overflow="auto"
      {...props}
    >
      <TypewriterEffect
        text={code}
        speed={speed}
        onComplete={onComplete}
        whiteSpace="pre-wrap"
      />
    </Box>
  );
};

export const AnimatedText = ({ 
  text, 
  animation = 'typewriter',
  speed = 50,
  ...props 
}) => {
  switch (animation) {
    case 'typewriter':
      return <TypewriterEffect text={text} speed={speed} {...props} />;
    case 'fade':
      return (
        <Typography variant="body1" 
          opacity={0}
          animation="fadeIn 1s ease-in-out forwards"
          {...props}
        >
          {text}
        </Typography>
      );
    case 'slide':
      return (
        <Typography variant="body1" 
          transform="translateX(-20px)"
          opacity={0}
          animation="slideIn 0.8s ease-out forwards"
          {...props}
        >
          {text}
        </Typography>
      );
    default:
      return <Typography variant="body1" {...props}>{text}</Typography>;
  }
};

// CSS animations for fade and slide effects
const styles = `
  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideIn {
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
