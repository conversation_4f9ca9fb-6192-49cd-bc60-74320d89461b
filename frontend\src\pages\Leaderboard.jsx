import { useEffect, useState } from 'react';
import { getLeaderboard } from '../api';
import {
 Box, Heading, Stack, Text, Badge 
} from '../components/ChakraToMui';

export default function Leaderboard() {
  const [users, setUsers] = useState([]);
  useEffect(() => {
    getLeaderboard().then(res => setUsers(res.data));
  }, []);
  return (
    <Box>
      <Heading mb={4}><PERSON><PERSON>ng xếp hạng</Heading>
      <Stack spacing={3}>
        {users.map((u, i) => (
          <Box key={u._id} p={4} borderWidth={1} borderRadius="md" display="flex" alignItems="center" gap={4}>
            <Text fontWeight="bold" fontSize="lg">#{i+1}</Text>
            <Text flex={1}>{u.name}</Text>
            <Text color="teal.500" fontWeight="bold">{u.points} điểm</Text>
            {u.badges && u.badges.map(b => <Badge key={b} colorScheme="yellow" ml={2}>{b}</Badge>)}
          </Box>
        ))}
      </Stack>
    </Box>
  );
} 