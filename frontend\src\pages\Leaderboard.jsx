import { useEffect, useState } from 'react';
import { getLeaderboard } from '../api';
import {
  Box,
  Typography,
  Stack,
  Chip
} from '@mui/material';

export default function Leaderboard() {
  const [users, setUsers] = useState([]);
  useEffect(() => {
    getLeaderboard().then(res => setUsers(res.data));
  }, []);
  return (
    <Box>
      <Typography variant="h4" component="h1" sx={{ mb: 4 }}>Bảng xếp hạng</Typography>
      <Stack spacing={3}>
        {users.map((u, i) => (
          <Box key={u._id} sx={{ p: 4 }} borderWidth={1} borderRadius="md" display="flex" alignItems="center" gasx={{ p: 4 }}>
            <Typography variant="body1" fontWeight="bold" fontSize="lg">#{i+1}</Typography>
            <Typography variant="body1" flex={1}>{u.name}</Typography>
            <Typography variant="body1" color="teal.500" fontWeight="bold">{u.points} điểm</Typography>
            {u.badges && u.badges.map(b => <Chip key={b} color="primary" ml={2}>{b}</Chip>)}
          </Box>
        ))}
      </Stack>
    </Box>
  );
} 