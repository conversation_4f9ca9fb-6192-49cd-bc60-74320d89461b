import React, { useState, useRef, useEffect, useCallback } from 'react';
import {

  Box, VStack, HStack, Text, Button, Textarea, Select, Switch,
  FormControl, FormLabel, Avatar, Card, CardBody, Progress,
  Modal, ModalOverlay, ModalContent, ModalHeader, ModalBody, ModalFooter,
  useDisclosure, Grid, GridItem, Badge, IconButton, Flex

} from '../components/ChakraToMui';
import { TypewriterEffect, CodeTypewriter } from './TypewriterEffect';
import { SmartLoader } from './LoadingAnimation';
import { Divider, Spacer } from './ChakraReplacements';
import { useToast } from './ToastProvider';
import * as api from '../api';

export const AdvancedChatbot = () => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [chatMode, setChatMode] = useState('general');
  const [aiPersonality, setAiPersonality] = useState('professional');
  const [contextAware, setContextAware] = useState(true);
  const [autoSuggest, setAutoSuggest] = useState(true);
  const [suggestions, setSuggestions] = useState([]);
  const [typingSpeed, setTypingSpeed] = useState(50);
  const [conversationHistory, setConversationHistory] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  
  const toast = useToast();
  const fileInputRef = useRef(null);
  const messagesEndRef = useRef(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const recognition = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window) {
      recognition.current = new window.webkitSpeechRecognition();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'vi-VN';
      
      recognition.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
        setIsListening(false);
        toast({ 
          title: "Đã nhận diện giọng nói", 
          description: transcript,
          status: "success" 
        });
      };
      
      recognition.current.onerror = () => {
        setIsListening(false);
        toast({ title: 'Lỗi nhận diện giọng nói', status: 'error' });
      };
    }
  }, [toast]);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Generate suggestions based on context
  useEffect(() => {
    if (autoSuggest && messages.length > 0) {
      generateSuggestions();
    }
  }, [messages, autoSuggest]);

  const generateSuggestions = useCallback(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.role === 'ai') {
      const contextSuggestions = generateContextualSuggestions(lastMessage.content, chatMode);
      setSuggestions(contextSuggestions);
    }
  }, [messages, chatMode]);

  const generateContextualSuggestions = (response, mode) => {
    const baseSuggestions = {
      general: ['Giải thích thêm', 'Cho ví dụ cụ thể', 'Có cách khác không?', 'Tóm tắt lại'],
      code: ['Giải thích code', 'Tối ưu hóa', 'Tìm lỗi', 'Viết test', 'Refactor code'],
      analysis: ['Phân tích sâu hơn', 'Tạo biểu đồ', 'So sánh dữ liệu', 'Đưa ra khuyến nghị'],
      creative: ['Phát triển ý tưởng', 'Tạo biến thể', 'Kết hợp với...', 'Làm sáng tạo hơn'],
      academic: ['Trích dẫn nguồn', 'Nghiên cứu liên quan', 'Phương pháp khác', 'Phân tích chi tiết']
    };
    
    return baseSuggestions[mode] || baseSuggestions.general;
  };

  const processFile = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;
        resolve(content.substring(0, 10000)); // Limit content length
      };
      reader.onerror = () => reject(new Error('Không thể đọc file'));
      reader.readAsText(file);
    });
  };

  const handleVoiceInput = () => {
    if (isListening) {
      recognition.current?.stop();
      setIsListening(false);
    } else {
      recognition.current?.start();
      setIsListening(true);
      toast({ title: 'Đang nghe...', status: 'info' });
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const allowedTypes = ['.txt', '.json', '.csv', '.md', '.js', '.py', '.html', '.css', '.jsx', '.ts', '.tsx'];
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
      
      if (!allowedTypes.includes(fileExtension)) {
        toast({ 
          title: 'File không được hỗ trợ', 
          description: `Chỉ hỗ trợ: ${allowedTypes.join(', ')}`,
          status: 'error' 
        });
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({ 
          title: 'File quá lớn', 
          description: 'Kích thước file tối đa 5MB',
          status: 'error' 
        });
        return;
      }
      
      setSelectedFile(file);
      toast({ 
        title: 'File đã được chọn', 
        description: `${file.name} (${(file.size / 1024).toFixed(1)} KB)`,
        status: 'success' 
      });
    }
  };

  const speakText = (text) => {
    if ('speechSynthesis' in window) {
      // Stop any ongoing speech
      speechSynthesis.cancel();
      
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'vi-VN';
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      
      utterance.onstart = () => {
        toast({ title: 'Đang đọc...', status: 'info' });
      };
      
      utterance.onend = () => {
        toast({ title: 'Đã đọc xong', status: 'success' });
      };
      
      speechSynthesis.speak(utterance);
    } else {
      toast({ title: 'Trình duyệt không hỗ trợ text-to-speech', status: 'error' });
    }
  };

  const handleSend = async (messageText = input) => {
    if (!messageText.trim() && !selectedFile) return;
    
    const userMessage = { 
      role: 'user', 
      content: messageText || 'Đã gửi file',
      timestamp: new Date(),
      mode: chatMode,
      personality: aiPersonality,
      hasFile: !!selectedFile
    };
    
    setMessages(msgs => [...msgs, userMessage]);
    setConversationHistory(prev => [...prev, userMessage]);
    setLoading(true);
    setIsTyping(true);
    setSuggestions([]);
    
    try {
      let fileContent = '';
      if (selectedFile) {
        fileContent = await processFile(selectedFile);
      }

      // Enhanced context for AI
      const context = {
        mode: chatMode,
        personality: aiPersonality,
        contextAware,
        conversationHistory: contextAware ? conversationHistory.slice(-5) : [],
        fileAttached: !!selectedFile,
        timestamp: new Date().toISOString()
      };

      const response = await api.askAI(messageText, {
        ...context,
        fileContent
      });
      
      const aiMessage = {
        role: 'ai',
        content: response.message || response.text || 'Xin lỗi, tôi không thể xử lý yêu cầu này.',
        timestamp: new Date(),
        mode: chatMode,
        personality: aiPersonality,
        suggestions: generateContextualSuggestions(response.message || response.text, chatMode)
      };

      setMessages(msgs => [...msgs, aiMessage]);
      setConversationHistory(prev => [...prev, aiMessage]);

    } catch (err) {
      console.error('AI Error:', err);
      toast({ 
        title: 'Lỗi AI', 
        description: err.response?.data?.message || 'Không thể kết nối với AI',
        status: 'error' 
      });
      
      const errorMessage = {
        role: 'ai',
        content: 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.',
        timestamp: new Date(),
        isError: true
      };
      setMessages(msgs => [...msgs, errorMessage]);
    }
    
    setLoading(false);
    setIsTyping(false);
    setInput('');
    setSelectedFile(null);
  };

  const clearChat = () => {
    setMessages([]);
    setConversationHistory([]);
    setSuggestions([]);
    toast({ title: 'Đã xóa cuộc trò chuyện', status: 'info' });
  };

  const exportChat = () => {
    const chatData = {
      messages,
      timestamp: new Date().toISOString(),
      mode: chatMode,
      personality: aiPersonality,
      totalMessages: messages.length
    };
    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fpt-unihub-chat-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast({ title: 'Đã xuất cuộc trò chuyện', status: 'success' });
  };

  return { 
    messages, input, setInput, loading, isListening, selectedFile, chatMode, setChatMode,
    aiPersonality, setAiPersonality, contextAware, setContextAware, autoSuggest, setAutoSuggest,
    suggestions, typingSpeed, setTypingSpeed, isTyping, fileInputRef, messagesEndRef,
    isOpen, onOpen, onClose, handleVoiceInput, handleFileSelect, speakText, handleSend,
    clearChat, exportChat, conversationHistory
  };
};

export default AdvancedChatbot;
