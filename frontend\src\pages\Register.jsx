import { useState } from 'react';
import {
 Box, Button, Input, Heading, Stack, Text 
} from '../components/ChakraToMui';
import { useNavigate } from 'react-router-dom';
import { register as apiRegister } from '../api';
import { useToast } from '../components/ToastProvider';
import { useAuth } from '../App';

export default function Register() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleRegister = async () => {
    if (!name || !email || !password) {
      toast({ title: 'Vui lòng điền đầy đủ thông tin', status: 'warning' });
      return;
    }

    if (password.length < 6) {
      toast({ title: '<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự', status: 'warning' });
      return;
    }

    setLoading(true);
    try {
      const res = await apiRegister(name, email, password);
      // Tự động đăng nhập sau khi đăng ký thành công
      if (res.data.token && res.data.user) {
        login(res.data.user, res.data.token);
        toast({ title: 'Đăng ký và đăng nhập thành công!', status: 'success' });
        navigate('/');
      } else {
        toast({ title: 'Đăng ký thành công', status: 'success' });
        navigate('/login');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.response?.data?.errors?.[0]?.msg || 'Đăng ký thất bại';
      toast({ title: 'Đăng ký thất bại', description: errorMessage, status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box maxW="sm" mx="auto" mt={16} p={8} borderWidth={1} borderRadius="lg" boxShadow="lg">
      <Heading mb={6} textAlign="center">Đăng ký</Heading>
      <Stack spacing={4}>
        <Input placeholder="Họ tên" value={name} onChange={e => setName(e.target.value)} />
        <Input placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
        <Input placeholder="Mật khẩu" type="password" value={password} onChange={e => setPassword(e.target.value)} />
        <Button colorScheme="teal" isLoading={loading} onClick={handleRegister}>Đăng ký</Button>
      </Stack>
      <Text mt={4} textAlign="center">Đã có tài khoản? <a href="/login" style={{color: '#3182ce'}}>Đăng nhập</a></Text>
    </Box>
  );
} 