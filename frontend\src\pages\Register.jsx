import { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Stack,
  Container,
  Paper,
  Link
} from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { register as apiRegister } from '../api';
import { useToast } from '../components/ToastProvider';
import { useAuth } from '../App';

export default function Register() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleRegister = async () => {
    if (!name || !email || !password) {
      toast({ title: 'Vui lòng điền đầy đủ thông tin', status: 'warning' });
      return;
    }

    if (password.length < 6) {
      toast({ title: '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự', status: 'warning' });
      return;
    }

    setLoading(true);
    try {
      const res = await apiRegister(name, email, password);
      // Tự động đăng nhập sau khi đăng ký thành công
      if (res.data.token && res.data.user) {
        login(res.data.user, res.data.token);
        toast({ title: 'Đăng ký và đăng nhập thành công!', status: 'success' });
        navigate('/');
      } else {
        toast({ title: 'Đăng ký thành công', status: 'success' });
        navigate('/login');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.response?.data?.errors?.[0]?.msg || 'Đăng ký thất bại';
      toast({ title: 'Đăng ký thất bại', description: errorMessage, status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ mt: 8 }}>
      <Paper
        elevation={8}
        sx={{
          p: 4,
          borderRadius: 3,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          textAlign="center"
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 4
          }}
        >
          🎓 Đăng ký FPT UniHub
        </Typography>

        <Stack spacing={3}>
          <TextField
            fullWidth
            label="Họ tên"
            variant="outlined"
            value={name}
            onChange={e => setName(e.target.value)}
            sx={{ borderRadius: 2 }}
          />
          <TextField
            fullWidth
            label="Email"
            type="email"
            variant="outlined"
            value={email}
            onChange={e => setEmail(e.target.value)}
            sx={{ borderRadius: 2 }}
          />
          <TextField
            fullWidth
            label="Mật khẩu"
            type="password"
            variant="outlined"
            value={password}
            onChange={e => setPassword(e.target.value)}
            helperText="Ít nhất 6 ký tự"
            sx={{ borderRadius: 2 }}
          />
          <Button
            variant="contained"
            size="large"
            disabled={loading}
            onClick={handleRegister}
            sx={{
              py: 1.5,
              borderRadius: 2,
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              fontWeight: 600,
              textTransform: 'none'
            }}
          >
            {loading ? 'Đang đăng ký...' : 'Đăng ký'}
          </Button>
        </Stack>

        <Typography variant="body2" textAlign="center" sx={{ mt: 3 }}>
          Đã có tài khoản?{' '}
          <Link
            component={RouterLink}
            to="/login"
            sx={{
              color: 'primary.main',
              textDecoration: 'none',
              fontWeight: 600,
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            Đăng nhập
          </Link>
        </Typography>
      </Paper>
    </Container>
  );
}