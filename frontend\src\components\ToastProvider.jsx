import React, { createContext, useContext, useState } from 'react';
import { Box, Typography, Snackbar, Alert } from '@mui/material';

const ToastContext = createContext();

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = ({ title, description, status = 'info', duration = 3000 }) => {
    const id = Date.now();
    const toast = { id, title, description, status, duration };
    
    setToasts(prev => [...prev, toast]);
    
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, duration);
  };

  const removeToast = (id) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  };

  const getSeverity = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      default: return 'info';
    }
  };

  return (
    <ToastContext.Provider value={addToast}>
      {children}
      <Box
        sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          zIndex: 9999,
          maxWidth: 400
        }}
      >
        {toasts.map(toast => (
          <Alert
            key={toast.id}
            severity={getSeverity(toast.status)}
            variant="filled"
            onClose={() => removeToast(toast.id)}
            sx={{
              mb: 1,
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateX(-5px)'
              }
            }}
          >
            <Typography variant="body2" fontWeight="bold">
              {toast.title}
            </Typography>
            {toast.description && (
              <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
                {toast.description}
              </Typography>
            )}
          </Alert>
        ))}
      </Box>
    </ToastContext.Provider>
  );
};
