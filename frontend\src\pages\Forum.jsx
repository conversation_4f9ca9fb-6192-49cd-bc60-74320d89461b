import { useEffect, useState } from 'react';
import { getForumPosts, createForumPost, commentForumPost, voteForumPost } from '../api';
import {
  Box,
  Typography,
  Stack,
  Button,
  TextField,
  Chip
} from '@mui/material';
import { useAuth } from '../App';
import { useToast } from '../components/ToastProvider';

export default function Forum() {
  const [posts, setPosts] = useState([]);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [comment, setComment] = useState('');
  const [selectedPost, setSelectedPost] = useState(null);
  const toast = useToast();
  const { user } = useAuth();

  const fetchPosts = async () => {
    const res = await getForumPosts();
    setPosts(res.data);
  };
  useEffect(() => { fetchPosts(); }, []);

  const handleCreate = async () => {
    if (!title || !content) return;
    await createForumPost({ title, content });
    setTitle(''); setContent('');
    fetchPosts();
    toast({ title: 'Đã đăng bài', status: 'success' });
  };

  const handleComment = async (postId) => {
    if (!comment) return;
    await commentForumPost({ postId, content: comment });
    setComment('');
    fetchPosts();
    toast({ title: 'Đã bình luận', status: 'success' });
  };

  const handleVote = async (postId, value) => {
    await voteForumPost({ postId, value });
    fetchPosts();
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" sx={{ mb: 4 }}>Diễn đàn</Typography>
      <Box sx={{ mb: 6 }} sx={{ p: 4 }} borderWidth={1} borderRadius="md">
        <TextField variant="outlined" placeholder="Tiêu đề bài viết" value={title} onChange={e => setTitle(e.target.value)} sx={{ mb: 2 }} />
        <Typography variant="body1"area placeholder="Nội dung" value={content} onChange={e => setContent(e.target.value)} sx={{ mb: 2 }} />
        <Button color="primary" onClick={handleCreate}>Đăng bài</Button>
      </Box>
      <Stack spacing={4}>
        {posts.map(post => (
          <Box key={post._id} sx={{ p: 4 }} borderWidth={1} borderRadius="md">
            <Typography variant="body1" fontWeight="bold" fontSize="lg">{post.title}</Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>{post.content}</Typography>
            <Typography variant="body1" fontSize="sm" color="gray.500">Bởi {post.user?.name} • {new Date(post.createdAt).toLocaleString()}</Typography>
            <Stack direction="row" align="center" sx={{ mt: 2 }} sx={{ mb: 2 }}>
              <Button size="sm" onClick={() => handleVote(post._id, 1)}>👍</Button>
              <Typography variant="body1">{post.votes}</Typography>
              <Button size="sm" onClick={() => handleVote(post._id, -1)}>👎</Button>
              <Button size="sm" onClick={() => setSelectedPost(selectedPost === post._id ? null : post._id)}>
                Bình luận ({post.comments.length})
              </Button>
            </Stack>
            {selectedPost === post._id && (
              <Box sx={{ mt: 2 }}>
                <Stack spacing={2}>
                  {post.comments.map(c => (
                    <Box key={c._id} sx={{ p: 2 }} borderWidth={1} borderRadius="md" bg="gray.50">
                      <Typography variant="body1" fontWeight="bold">{c.user?.name}</Typography>
                      <Typography variant="body1">{c.content}</Typography>
                      <Typography variant="body1" fontSize="xs" color="gray.400">{new Date(c.createdAt).toLocaleString()}</Typography>
                    </Box>
                  ))}
                </Stack>
                <Stack direction="row" sx={{ mt: 2 }}>
                  <TextField variant="outlined" placeholder="Bình luận..." value={comment} onChange={e => setComment(e.target.value)} />
                  <Button color="primary" onClick={() => handleComment(post._id)}>Gửi</Button>
                </Stack>
              </Box>
            )}
          </Box>
        ))}
      </Stack>
    </Box>
  );
} 