import { useEffect, useState } from 'react';
import { getForumPosts, createForumPost, commentForumPost, voteForumPost } from '../api';
import {
 Box, Heading, Stack, Text, Button, Input, Textarea, Badge 
} from '../components/ChakraToMui';
import { useAuth } from '../App';
import { useToast } from '../components/ToastProvider';

export default function Forum() {
  const [posts, setPosts] = useState([]);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [comment, setComment] = useState('');
  const [selectedPost, setSelectedPost] = useState(null);
  const toast = useToast();
  const { user } = useAuth();

  const fetchPosts = async () => {
    const res = await getForumPosts();
    setPosts(res.data);
  };
  useEffect(() => { fetchPosts(); }, []);

  const handleCreate = async () => {
    if (!title || !content) return;
    await createForumPost({ title, content });
    setTitle(''); setContent('');
    fetchPosts();
    toast({ title: 'Đã đăng bài', status: 'success' });
  };

  const handleComment = async (postId) => {
    if (!comment) return;
    await commentForumPost({ postId, content: comment });
    setComment('');
    fetchPosts();
    toast({ title: 'Đã bình luận', status: 'success' });
  };

  const handleVote = async (postId, value) => {
    await voteForumPost({ postId, value });
    fetchPosts();
  };

  return (
    <Box>
      <Heading mb={4}>Diễn đàn</Heading>
      <Box mb={6} p={4} borderWidth={1} borderRadius="md">
        <Input placeholder="Tiêu đề bài viết" value={title} onChange={e => setTitle(e.target.value)} mb={2} />
        <Textarea placeholder="Nội dung" value={content} onChange={e => setContent(e.target.value)} mb={2} />
        <Button colorScheme="teal" onClick={handleCreate}>Đăng bài</Button>
      </Box>
      <Stack spacing={4}>
        {posts.map(post => (
          <Box key={post._id} p={4} borderWidth={1} borderRadius="md">
            <Text fontWeight="bold" fontSize="lg">{post.title}</Text>
            <Text mb={2}>{post.content}</Text>
            <Text fontSize="sm" color="gray.500">Bởi {post.user?.name} • {new Date(post.createdAt).toLocaleString()}</Text>
            <Stack direction="row" align="center" mt={2} mb={2}>
              <Button size="sm" onClick={() => handleVote(post._id, 1)}>👍</Button>
              <Text>{post.votes}</Text>
              <Button size="sm" onClick={() => handleVote(post._id, -1)}>👎</Button>
              <Button size="sm" onClick={() => setSelectedPost(selectedPost === post._id ? null : post._id)}>
                Bình luận ({post.comments.length})
              </Button>
            </Stack>
            {selectedPost === post._id && (
              <Box mt={2}>
                <Stack spacing={2}>
                  {post.comments.map(c => (
                    <Box key={c._id} p={2} borderWidth={1} borderRadius="md" bg="gray.50">
                      <Text fontWeight="bold">{c.user?.name}</Text>
                      <Text>{c.content}</Text>
                      <Text fontSize="xs" color="gray.400">{new Date(c.createdAt).toLocaleString()}</Text>
                    </Box>
                  ))}
                </Stack>
                <Stack direction="row" mt={2}>
                  <Input placeholder="Bình luận..." value={comment} onChange={e => setComment(e.target.value)} />
                  <Button colorScheme="teal" onClick={() => handleComment(post._id)}>Gửi</Button>
                </Stack>
              </Box>
            )}
          </Box>
        ))}
      </Stack>
    </Box>
  );
} 