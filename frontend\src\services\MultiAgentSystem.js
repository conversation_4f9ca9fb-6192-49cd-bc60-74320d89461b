/**
 * Enhanced Multi-Agent AI System
 * Manages multiple AI agents with specialized capabilities
 */

class Agent {
  constructor(name, capabilities, config = {}) {
    this.id = `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.name = name;
    this.capabilities = capabilities;
    this.config = {
      maxConcurrentTasks: 3,
      timeout: 30000,
      retryAttempts: 3,
      ...config
    };
    this.status = 'idle'; // idle, busy, error, offline
    this.currentTasks = new Map();
    this.completedTasks = 0;
    this.errorCount = 0;
    this.averageResponseTime = 0;
    this.lastActivity = Date.now();
  }

  async executeTask(task) {
    if (this.currentTasks.size >= this.config.maxConcurrentTasks) {
      throw new Error(`Agent ${this.name} is at maximum capacity`);
    }

    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();
    
    this.currentTasks.set(taskId, {
      ...task,
      startTime,
      status: 'running'
    });
    
    this.status = 'busy';
    this.lastActivity = Date.now();

    try {
      const result = await this._processTask(task);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Update metrics
      this.completedTasks++;
      this.averageResponseTime = (
        (this.averageResponseTime * (this.completedTasks - 1)) + responseTime
      ) / this.completedTasks;
      
      this.currentTasks.delete(taskId);
      this.status = this.currentTasks.size > 0 ? 'busy' : 'idle';
      
      return {
        success: true,
        result,
        responseTime,
        agentId: this.id,
        taskId
      };
    } catch (error) {
      this.errorCount++;
      this.currentTasks.delete(taskId);
      this.status = this.currentTasks.size > 0 ? 'busy' : 'error';
      
      throw {
        success: false,
        error: error.message,
        agentId: this.id,
        taskId
      };
    }
  }

  async _processTask(task) {
    // This would be implemented by specific agent types
    throw new Error('_processTask must be implemented by subclass');
  }

  getMetrics() {
    return {
      id: this.id,
      name: this.name,
      status: this.status,
      capabilities: this.capabilities,
      completedTasks: this.completedTasks,
      errorCount: this.errorCount,
      errorRate: this.completedTasks > 0 ? (this.errorCount / this.completedTasks) * 100 : 0,
      averageResponseTime: this.averageResponseTime,
      currentLoad: this.currentTasks.size,
      maxCapacity: this.config.maxConcurrentTasks,
      lastActivity: this.lastActivity
    };
  }

  isHealthy() {
    const now = Date.now();
    const inactiveTime = now - this.lastActivity;
    const errorRate = this.completedTasks > 0 ? (this.errorCount / this.completedTasks) * 100 : 0;
    
    return (
      this.status !== 'offline' &&
      inactiveTime < 300000 && // 5 minutes
      errorRate < 50 // Less than 50% error rate
    );
  }
}

// Specialized AI Agents
class ChatAgent extends Agent {
  constructor(config = {}) {
    super('ChatAgent', ['conversation', 'qa', 'support'], config);
  }

  async _processTask(task) {
    const { type, message, context } = task;
    
    // Simulate API call to AI service
    const response = await fetch('/api/ai/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message, context, type })
    });
    
    if (!response.ok) {
      throw new Error(`Chat API error: ${response.statusText}`);
    }
    
    return await response.json();
  }
}

class StudyAgent extends Agent {
  constructor(config = {}) {
    super('StudyAgent', ['study_plan', 'quiz_generation', 'progress_tracking'], config);
  }

  async _processTask(task) {
    const { type, subject, difficulty, requirements } = task;
    
    const response = await fetch('/api/ai/study', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type, subject, difficulty, requirements })
    });
    
    if (!response.ok) {
      throw new Error(`Study API error: ${response.statusText}`);
    }
    
    return await response.json();
  }
}

class AnalyticsAgent extends Agent {
  constructor(config = {}) {
    super('AnalyticsAgent', ['data_analysis', 'reporting', 'insights'], config);
  }

  async _processTask(task) {
    const { type, data, metrics } = task;
    
    const response = await fetch('/api/ai/analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type, data, metrics })
    });
    
    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.statusText}`);
    }
    
    return await response.json();
  }
}

// Task Queue Manager
class TaskQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.maxRetries = 3;
  }

  addTask(task, priority = 'normal') {
    const queueItem = {
      id: `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      task,
      priority,
      retries: 0,
      createdAt: Date.now(),
      status: 'pending'
    };

    // Insert based on priority
    if (priority === 'high') {
      this.queue.unshift(queueItem);
    } else {
      this.queue.push(queueItem);
    }

    return queueItem.id;
  }

  getNextTask() {
    return this.queue.find(item => item.status === 'pending');
  }

  updateTaskStatus(taskId, status, result = null, error = null) {
    const task = this.queue.find(item => item.id === taskId);
    if (task) {
      task.status = status;
      task.result = result;
      task.error = error;
      task.completedAt = Date.now();
    }
  }

  removeCompletedTasks() {
    this.queue = this.queue.filter(item => 
      item.status !== 'completed' && item.status !== 'failed'
    );
  }

  getQueueMetrics() {
    const total = this.queue.length;
    const pending = this.queue.filter(t => t.status === 'pending').length;
    const processing = this.queue.filter(t => t.status === 'processing').length;
    const completed = this.queue.filter(t => t.status === 'completed').length;
    const failed = this.queue.filter(t => t.status === 'failed').length;

    return { total, pending, processing, completed, failed };
  }
}

// Main Multi-Agent System
class MultiAgentSystem {
  constructor() {
    this.agents = new Map();
    this.taskQueue = new TaskQueue();
    this.isRunning = false;
    this.metrics = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageResponseTime: 0
    };
    
    this.initializeAgents();
    this.startHealthMonitoring();
  }

  initializeAgents() {
    // Create default agents
    this.addAgent(new ChatAgent());
    this.addAgent(new StudyAgent());
    this.addAgent(new AnalyticsAgent());
  }

  addAgent(agent) {
    this.agents.set(agent.id, agent);
    console.log(`Agent ${agent.name} (${agent.id}) added to system`);
  }

  removeAgent(agentId) {
    const agent = this.agents.get(agentId);
    if (agent) {
      this.agents.delete(agentId);
      console.log(`Agent ${agent.name} (${agentId}) removed from system`);
    }
  }

  async executeTask(taskType, taskData, priority = 'normal') {
    const task = {
      type: taskType,
      ...taskData,
      timestamp: Date.now()
    };

    const taskId = this.taskQueue.addTask(task, priority);
    this.metrics.totalTasks++;

    if (!this.isRunning) {
      this.start();
    }

    return new Promise((resolve, reject) => {
      const checkCompletion = () => {
        const queueItem = this.taskQueue.queue.find(item => item.id === taskId);
        
        if (!queueItem) {
          reject(new Error('Task not found'));
          return;
        }

        if (queueItem.status === 'completed') {
          this.metrics.completedTasks++;
          resolve(queueItem.result);
        } else if (queueItem.status === 'failed') {
          this.metrics.failedTasks++;
          reject(queueItem.error);
        } else {
          setTimeout(checkCompletion, 100);
        }
      };

      checkCompletion();
    });
  }

  findBestAgent(task) {
    const availableAgents = Array.from(this.agents.values())
      .filter(agent => 
        agent.isHealthy() && 
        agent.capabilities.includes(task.type) &&
        agent.currentTasks.size < agent.config.maxConcurrentTasks
      )
      .sort((a, b) => {
        // Sort by load and performance
        const loadA = a.currentTasks.size / a.config.maxConcurrentTasks;
        const loadB = b.currentTasks.size / b.config.maxConcurrentTasks;
        
        if (loadA !== loadB) return loadA - loadB;
        return a.averageResponseTime - b.averageResponseTime;
      });

    return availableAgents[0] || null;
  }

  async start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('Multi-Agent System started');

    while (this.isRunning) {
      const nextTask = this.taskQueue.getNextTask();
      
      if (!nextTask) {
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }

      const agent = this.findBestAgent(nextTask.task);
      
      if (!agent) {
        // No available agent, wait and retry
        await new Promise(resolve => setTimeout(resolve, 500));
        continue;
      }

      // Update task status
      this.taskQueue.updateTaskStatus(nextTask.id, 'processing');

      try {
        const result = await agent.executeTask(nextTask.task);
        this.taskQueue.updateTaskStatus(nextTask.id, 'completed', result);
      } catch (error) {
        nextTask.retries++;
        
        if (nextTask.retries < this.taskQueue.maxRetries) {
          this.taskQueue.updateTaskStatus(nextTask.id, 'pending');
        } else {
          this.taskQueue.updateTaskStatus(nextTask.id, 'failed', null, error);
        }
      }

      // Clean up completed tasks periodically
      if (Math.random() < 0.1) {
        this.taskQueue.removeCompletedTasks();
      }
    }
  }

  stop() {
    this.isRunning = false;
    console.log('Multi-Agent System stopped');
  }

  startHealthMonitoring() {
    setInterval(() => {
      this.agents.forEach(agent => {
        if (!agent.isHealthy()) {
          console.warn(`Agent ${agent.name} (${agent.id}) is unhealthy:`, agent.getMetrics());
        }
      });
    }, 30000); // Check every 30 seconds
  }

  getSystemMetrics() {
    const agentMetrics = Array.from(this.agents.values()).map(agent => agent.getMetrics());
    const queueMetrics = this.taskQueue.getQueueMetrics();
    
    return {
      system: this.metrics,
      agents: agentMetrics,
      queue: queueMetrics,
      isRunning: this.isRunning,
      timestamp: Date.now()
    };
  }
}

// Singleton instance
const multiAgentSystem = new MultiAgentSystem();

export default multiAgentSystem;
export { Agent, ChatAgent, StudyAgent, AnalyticsAgent, TaskQueue, MultiAgentSystem };
