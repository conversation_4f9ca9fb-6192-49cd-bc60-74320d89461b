import { useState, useEffect, useCallback, useRef } from 'react';
import multiAgentSystem from '../services/MultiAgentSystem';

/**
 * Hook to interact with the Multi-Agent AI System
 */
export const useMultiAgent = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const metricsInterval = useRef(null);

  useEffect(() => {
    // Initialize connection
    setIsConnected(multiAgentSystem.isRunning);
    
    // Start metrics monitoring
    metricsInterval.current = setInterval(() => {
      const systemMetrics = multiAgentSystem.getSystemMetrics();
      setMetrics(systemMetrics);
    }, 2000);

    return () => {
      if (metricsInterval.current) {
        clearInterval(metricsInterval.current);
      }
    };
  }, []);

  const executeTask = useCallback(async (taskType, taskData, priority = 'normal') => {
    setLoading(true);
    setError(null);

    try {
      const result = await multiAgentSystem.executeTask(taskType, taskData, priority);
      setLoading(false);
      return result;
    } catch (err) {
      setError(err.message || 'Task execution failed');
      setLoading(false);
      throw err;
    }
  }, []);

  const startSystem = useCallback(() => {
    multiAgentSystem.start();
    setIsConnected(true);
  }, []);

  const stopSystem = useCallback(() => {
    multiAgentSystem.stop();
    setIsConnected(false);
  }, []);

  return {
    isConnected,
    metrics,
    loading,
    error,
    executeTask,
    startSystem,
    stopSystem
  };
};

/**
 * Hook specifically for chat interactions
 */
export const useChatAgent = () => {
  const { executeTask, loading, error } = useMultiAgent();

  const sendMessage = useCallback(async (message, context = {}) => {
    return executeTask('conversation', {
      message,
      context,
      timestamp: Date.now()
    }, 'high');
  }, [executeTask]);

  const askQuestion = useCallback(async (question, subject = null) => {
    return executeTask('qa', {
      question,
      subject,
      timestamp: Date.now()
    });
  }, [executeTask]);

  const getSupport = useCallback(async (issue, category = 'general') => {
    return executeTask('support', {
      issue,
      category,
      timestamp: Date.now()
    });
  }, [executeTask]);

  return {
    sendMessage,
    askQuestion,
    getSupport,
    loading,
    error
  };
};

/**
 * Hook for study-related AI tasks
 */
export const useStudyAgent = () => {
  const { executeTask, loading, error } = useMultiAgent();

  const generateStudyPlan = useCallback(async (subject, goals, timeframe) => {
    return executeTask('study_plan', {
      subject,
      goals,
      timeframe,
      timestamp: Date.now()
    });
  }, [executeTask]);

  const generateQuiz = useCallback(async (subject, difficulty = 'medium', questionCount = 10) => {
    return executeTask('quiz_generation', {
      subject,
      difficulty,
      questionCount,
      timestamp: Date.now()
    });
  }, [executeTask]);

  const trackProgress = useCallback(async (userId, activities) => {
    return executeTask('progress_tracking', {
      userId,
      activities,
      timestamp: Date.now()
    });
  }, [executeTask]);

  return {
    generateStudyPlan,
    generateQuiz,
    trackProgress,
    loading,
    error
  };
};

/**
 * Hook for analytics and insights
 */
export const useAnalyticsAgent = () => {
  const { executeTask, loading, error } = useMultiAgent();

  const analyzeData = useCallback(async (data, analysisType = 'general') => {
    return executeTask('data_analysis', {
      data,
      analysisType,
      timestamp: Date.now()
    });
  }, [executeTask]);

  const generateReport = useCallback(async (reportType, parameters) => {
    return executeTask('reporting', {
      reportType,
      parameters,
      timestamp: Date.now()
    });
  }, [executeTask]);

  const getInsights = useCallback(async (domain, metrics) => {
    return executeTask('insights', {
      domain,
      metrics,
      timestamp: Date.now()
    });
  }, [executeTask]);

  return {
    analyzeData,
    generateReport,
    getInsights,
    loading,
    error
  };
};

/**
 * Hook for monitoring agent performance
 */
export const useAgentMonitoring = () => {
  const [agentMetrics, setAgentMetrics] = useState([]);
  const [systemHealth, setSystemHealth] = useState('unknown');
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    const interval = setInterval(() => {
      const metrics = multiAgentSystem.getSystemMetrics();
      
      if (metrics) {
        setAgentMetrics(metrics.agents);
        
        // Calculate system health
        const healthyAgents = metrics.agents.filter(agent => 
          agent.status !== 'error' && agent.errorRate < 50
        ).length;
        
        const totalAgents = metrics.agents.length;
        const healthPercentage = totalAgents > 0 ? (healthyAgents / totalAgents) * 100 : 0;
        
        if (healthPercentage >= 80) {
          setSystemHealth('healthy');
        } else if (healthPercentage >= 50) {
          setSystemHealth('warning');
        } else {
          setSystemHealth('critical');
        }

        // Generate alerts for problematic agents
        const newAlerts = metrics.agents
          .filter(agent => agent.errorRate > 30 || agent.status === 'error')
          .map(agent => ({
            id: `alert_${agent.id}_${Date.now()}`,
            type: 'agent_error',
            severity: agent.errorRate > 50 ? 'high' : 'medium',
            message: `Agent ${agent.name} has ${agent.errorRate.toFixed(1)}% error rate`,
            timestamp: Date.now(),
            agentId: agent.id
          }));

        setAlerts(prev => [...prev, ...newAlerts].slice(-10)); // Keep last 10 alerts
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const dismissAlert = useCallback((alertId) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  const getAgentById = useCallback((agentId) => {
    return agentMetrics.find(agent => agent.id === agentId);
  }, [agentMetrics]);

  return {
    agentMetrics,
    systemHealth,
    alerts,
    dismissAlert,
    getAgentById
  };
};

/**
 * Hook for batch processing multiple tasks
 */
export const useBatchProcessor = () => {
  const { executeTask } = useMultiAgent();
  const [batchStatus, setBatchStatus] = useState({
    total: 0,
    completed: 0,
    failed: 0,
    inProgress: false
  });

  const processBatch = useCallback(async (tasks, options = {}) => {
    const { concurrency = 3, priority = 'normal' } = options;
    
    setBatchStatus({
      total: tasks.length,
      completed: 0,
      failed: 0,
      inProgress: true
    });

    const results = [];
    
    for (let i = 0; i < tasks.length; i += concurrency) {
      const batch = tasks.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (task) => {
        try {
          const result = await executeTask(task.type, task.data, priority);
          setBatchStatus(prev => ({
            ...prev,
            completed: prev.completed + 1
          }));
          return { success: true, result, task };
        } catch (error) {
          setBatchStatus(prev => ({
            ...prev,
            failed: prev.failed + 1
          }));
          return { success: false, error, task };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    setBatchStatus(prev => ({ ...prev, inProgress: false }));
    return results;
  }, [executeTask]);

  return {
    processBatch,
    batchStatus
  };
};

export default {
  useMultiAgent,
  useChatAgent,
  useStudyAgent,
  useAnalyticsAgent,
  useAgentMonitoring,
  useBatchProcessor
};
