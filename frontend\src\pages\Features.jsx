import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Button,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  LinearProgress,
  Avatar
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { TypewriterEffect } from '../components/TypewriterEffect';
import { SmartLoader } from '../components/LoadingAnimation';
import {
  Divider,
  Spacer,
  AvatarGroup
} from '@mui/material';

export default function Features() {
  const [activeTab, setActiveTab] = useState(0);
  
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, blue.900, purple.900, pink.900)'
  );

  const featureCategories = [
    {
      name: "🤖 AI & Machine Learning",
      color: "purple",
      features: [
        {
          title: "AI Chatbot Si<PERSON><PERSON>",
          description: "Chatbot AI với Gemini API, hỗ trợ voice, file analysis, code generation",
          status: "completed",
          demo: "/chatbot",
          highlights: ["Voice Recognition", "File Analysis", "Code Generation", "Context Aware"]
        },
        {
          title: "Multiple AI Personalities",
          description: "AI với nhiều tính cách: Professional, Friendly, Expert, Casual",
          status: "completed",
          demo: "/chatbot",
          highlights: ["4 Personalities", "Customizable", "Context Switching"]
        },
        {
          title: "AI Modes",
          description: "Chế độ AI chuyên biệt: General, Code, Analysis, Creative, Academic",
          status: "completed",
          demo: "/chatbot",
          highlights: ["5 Modes", "Specialized", "Smart Suggestions"]
        }
      ]
    },
    {
      name: "🎨 Frontend & UI/UX",
      color: "blue",
      features: [
        {
          title: "Modern React 19 + Chakra UI v3",
          description: "Giao diện hiện đại với React 19 và Chakra UI v3",
          status: "completed",
          demo: "/",
          highlights: ["React 19", "Chakra UI v3", "Responsive", "Modern Design"]
        },
        {
          title: "Custom Animations",
          description: "TypeWriter effects, Loading animations, Transitions",
          status: "completed",
          demo: "/demo",
          highlights: ["TypeWriter", "Loading Animations", "Smooth Transitions"]
        },
        {
          title: "Toast Notification System",
          description: "Hệ thống thông báo tùy chỉnh đẹp mắt",
          status: "completed",
          demo: "/demo",
          highlights: ["Custom Toast", "Multiple Types", "Auto Dismiss"]
        }
      ]
    },
    {
      name: "⚡ Backend & API",
      color: "green",
      features: [
        {
          title: "Node.js + Express + MongoDB",
          description: "Backend mạnh mẽ với Node.js, Express 5, MongoDB",
          status: "completed",
          demo: null,
          highlights: ["Node.js", "Express 5", "MongoDB", "Mongoose"]
        },
        {
          title: "Authentication System",
          description: "JWT + Google OAuth authentication",
          status: "completed",
          demo: "/login",
          highlights: ["JWT", "Google OAuth", "Secure", "Session Management"]
        },
        {
          title: "Real-time Communication",
          description: "Socket.io cho chat real-time và notifications",
          status: "completed",
          demo: "/group",
          highlights: ["Socket.io", "Real-time Chat", "Live Updates"]
        }
      ]
    },
    {
      name: "🔧 Core Features",
      color: "orange",
      features: [
        {
          title: "Event Management",
          description: "Quản lý sự kiện, workshop, đăng ký tham gia",
          status: "completed",
          demo: "/event",
          highlights: ["Create Events", "Register", "Calendar Integration"]
        },
        {
          title: "Group Chat System",
          description: "Chat nhóm real-time với AI summarization",
          status: "completed",
          demo: "/group",
          highlights: ["Real-time Chat", "AI Summary", "File Sharing"]
        },
        {
          title: "Smart Todo System",
          description: "Todo list với AI suggestions và deadline tracking",
          status: "completed",
          demo: "/todo",
          highlights: ["AI Suggestions", "Deadline Tracking", "Priority System"]
        },
        {
          title: "Forum & Community",
          description: "Diễn đàn cộng đồng với voting và commenting",
          status: "completed",
          demo: "/forum",
          highlights: ["Voting System", "Comments", "Community Driven"]
        }
      ]
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'green';
      case 'in-progress': return 'orange';
      case 'planned': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'in-progress': return 'Đang phát triển';
      case 'planned': return 'Kế hoạch';
      default: return 'Chưa xác định';
    }
  };

  return (
    <Box bgGradient={bgGradient} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <Stack direction="column" spacing={8} align="stretch">
          {/* Header */}
          <Box textAlign="center">
            <TypewriterEffect
              text="✨ Tính năng FPT UniHub"
              fontSize="4xl"
              fontWeight="bold"
              bgGradient="linear(to-r, blue.400, purple.400, pink.400)"
              bgClip="text"
              sx={{ mb: 4 }}
            />
            <Typography variant="body1" fontSize="xl" color="gray.600" maxW="2xl" mx="auto">
              Khám phá toàn bộ tính năng đã được phát triển và hoàn thiện
            </Typography>
          </Box>

          {/* Feature Categories */}
          <Tabs index={activeTab} onChange={setActiveTab} variant="enclosed">
            <TabList>
              {featureCategories.map((category, index) => (
                <Tab key={index} _selected={{ color: `${category.color}.500`, bg: 'white' }}>
                  {category.name}
                </Tab>
              ))}
            </TabList>

            <TabPanels>
              {featureCategories.map((category, categoryIndex) => (
                <TabPanel key={categoryIndex}>
                  <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                    {category.features.map((feature, featureIndex) => (
                      <Card 
                        key={featureIndex}
                        bg="white"
                        boxShadow="lg"
                        borderRadius="xl"
                        overflow="hidden"
                        transition="all 0.3s"
                        _hover={{ transform: "translateY(-5px)", boxShadow: "xl" }}
                      >
                        <CardHeader pb={2}>
                          <Box display="flex" align="center" justify="space-between" sx={{ mb: 2 }}>
                            <Chip 
                              colorScheme={getStatusColor(feature.status)}
                              variant="solid"
                              borderRadius="full"
                            >
                              {getStatusText(feature.status)}
                            </Chip>
                            {feature.status === 'completed' && (
                              <Typography variant="body1" fontSize="2xl">✅</Typography>
                            )}
                          </Box>
                          <Typography variant="h4" component="h1" size="md" sx={{ mb: 2 }}>
                            {feature.title}
                          </Typography>
                          <Typography variant="body1" fontSize="sm" color="gray.600">
                            {feature.description}
                          </Typography>
                        </CardHeader>
                        
                        <CardContent pt={0}>
                          <Stack direction="column" align="stretch" spacing={3}>
                            {/* Highlights */}
                            <Box>
                              <Typography variant="body1" fontSize="sm" fontWeight="bold" sx={{ mb: 2 }}>
                                Điểm nổi bật:
                              </Typography>
                              <SimpleGrid columns={2} spacing={1}>
                                {feature.highlights.map((highlight, index) => (
                                  <Chip 
                                    key={index}
                                    variant="outline" 
                                    colorScheme={category.color}
                                    fontSize="xs"
                                    textAlign="center"
                                  >
                                    {highlight}
                                  </Chip>
                                ))}
                              </SimpleGrid>
                            </Box>

                            <Divider />

                            {/* Demo Button */}
                            {feature.demo ? (
                              <Button
                                as={RouterLink}
                                to={feature.demo}
                                colorScheme={category.color}
                                size="sm"
                                w="full"
                              >
                                🎯 Xem Demo
                              </Button>
                            ) : (
                              <Button
                                size="sm"
                                variant="outline"
                                disabled
                                w="full"
                              >
                                Backend Feature
                              </Button>
                            )}
                          </Stack>
                        </CardContent>
                      </Card>
                    ))}
                  </SimpleGrid>
                </TabPanel>
              ))}
            </TabPanels>
          </Tabs>

          {/* Overall Progress */}
          <Card bg="white" boxShadow="xl">
            <CardHeader>
              <Typography variant="h4" component="h1" size="lg" textAlign="center">
                📊 Tiến độ tổng thể
              </Typography>
            </CardHeader>
            <CardContent>
              <Stack direction="column" spacing={4}>
                <Typography variant="body1" fontSize="lg" textAlign="center">
                  Dự án đã hoàn thành <strong>100%</strong> các tính năng chính
                </Typography>
                <LinearProgress value={100} color="primary" size="lg" w="full" borderRadius="full" />
                <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} w="full">
                  <Box textAlign="center">
                    <Typography variant="body1" fontSize="2xl" fontWeight="bold" color="green.500">15+</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">Tính năng</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography variant="body1" fontSize="2xl" fontWeight="bold" color="blue.500">30+</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">Components</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography variant="body1" fontSize="2xl" fontWeight="bold" color="purple.500">40+</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">API Endpoints</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography variant="body1" fontSize="2xl" fontWeight="bold" color="orange.500">9000+</Typography>
                    <Typography variant="body1" fontSize="sm" color="gray.600">Lines of Code</Typography>
                  </Box>
                </SimpleGrid>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </Box>
  );
}
