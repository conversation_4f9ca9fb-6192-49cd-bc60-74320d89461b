import React from 'react';
import {
  Box,
  Divider as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Paper,
  Container as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  LinearProgress,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  useTheme
} from '@mui/material';

// Spacer component to replace Chakra UI Spacer
export const Spacer = ({ size = 1, direction = 'vertical' }) => {
  const spacing = size * 8; // MUI uses 8px base spacing
  
  if (direction === 'horizontal') {
    return <Box sx={{ width: spacing }} />;
  }
  
  return <Box sx={{ height: spacing }} />;
};

// Divider component to replace Chakra UI Divider
export const Divider = ({ orientation = 'horizontal', ...props }) => {
  return (
    <MuiDivider 
      orientation={orientation} 
      {...props}
    />
  );
};

// Stack component replacement (though <PERSON><PERSON> has Stack, this ensures compatibility)
export const Stack = ({ children, direction = 'column', spacing = 1, ...props }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: direction === 'row' ? 'row' : 'column',
        gap: spacing * 8, // Convert to pixels
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// HStack - horizontal stack
export const HStack = ({ children, spacing = 1, ...props }) => {
  return (
    <Stack direction="row" spacing={spacing} {...props}>
      {children}
    </Stack>
  );
};

// VStack - vertical stack  
export const VStack = ({ children, spacing = 1, ...props }) => {
  return (
    <Stack direction="column" spacing={spacing} {...props}>
      {children}
    </Stack>
  );
};

// Center component
export const Center = ({ children, ...props }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// Flex component
export const Flex = ({ children, direction = 'row', align = 'stretch', justify = 'flex-start', ...props }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: direction,
        alignItems: align,
        justifyContent: justify,
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// Text component replacement
export const Text = ({ children, fontSize, fontWeight, color, ...props }) => {
  const theme = useTheme();

  return (
    <Typography
      variant="body1"
      sx={{
        fontSize: fontSize || 'inherit',
        fontWeight: fontWeight || 'normal',
        color: color || 'inherit',
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Typography>
  );
};

// Heading component replacement
export const Heading = ({ children, size = 'md', ...props }) => {
  const variantMap = {
    xs: 'h6',
    sm: 'h5',
    md: 'h4',
    lg: 'h3',
    xl: 'h2',
    '2xl': 'h1'
  };

  return (
    <Typography
      variant={variantMap[size] || 'h4'}
      component={variantMap[size] || 'h4'}
      {...props}
    >
      {children}
    </Typography>
  );
};

// Input component replacement
export const Input = ({ placeholder, value, onChange, ...props }) => {
  return (
    <TextField
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      variant="outlined"
      fullWidth
      {...props}
    />
  );
};

// Button component replacement
export const Button = ({ children, colorScheme = 'primary', variant = 'contained', size = 'medium', ...props }) => {
  const colorMap = {
    blue: 'primary',
    green: 'success',
    red: 'error',
    orange: 'warning',
    gray: 'inherit'
  };

  return (
    <MuiButton
      variant={variant}
      color={colorMap[colorScheme] || colorScheme}
      size={size}
      {...props}
    >
      {children}
    </MuiButton>
  );
};

// Container component replacement
export const Container = ({ children, maxW = 'lg', ...props }) => {
  const maxWidthMap = {
    sm: 'sm',
    md: 'md',
    lg: 'lg',
    xl: 'xl',
    '2xl': false
  };

  return (
    <MuiContainer
      maxWidth={maxWidthMap[maxW] || maxW}
      {...props}
    >
      {children}
    </MuiContainer>
  );
};

// SimpleGrid component replacement
export const SimpleGrid = ({ children, columns = 1, spacing = 2, ...props }) => {
  return (
    <Grid container spacing={spacing} {...props}>
      {React.Children.map(children, (child, index) => (
        <Grid item xs={12} sm={12/columns} key={index}>
          {child}
        </Grid>
      ))}
    </Grid>
  );
};

// Card-like Box component
export const Card = ({ children, ...props }) => {
  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        borderRadius: 2,
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Paper>
  );
};

// Loading Spinner
export const Spinner = ({ size = 'medium', color = 'primary', ...props }) => {
  const sizeMap = {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 32,
    xl: 40
  };

  return (
    <CircularProgress
      size={sizeMap[size] || size}
      color={color}
      {...props}
    />
  );
};

// Alert component replacement
export const AlertComponent = ({ status = 'info', children, ...props }) => {
  const severityMap = {
    success: 'success',
    error: 'error',
    warning: 'warning',
    info: 'info'
  };

  return (
    <Alert severity={severityMap[status]} {...props}>
      {children}
    </Alert>
  );
};

// Badge component replacement
export const Badge = ({ children, colorScheme = 'gray', ...props }) => {
  const colorMap = {
    gray: 'default',
    blue: 'primary',
    green: 'success',
    red: 'error',
    orange: 'warning'
  };

  return (
    <Chip
      label={children}
      color={colorMap[colorScheme]}
      size="small"
      {...props}
    />
  );
};

export default {
  Spacer,
  Divider,
  Stack,
  HStack,
  VStack,
  Center,
  Flex,
  Text,
  Heading,
  Input,
  Button,
  Container,
  SimpleGrid,
  Card,
  Spinner,
  Alert: AlertComponent,
  Badge
};
