import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  login, 
  register, 
  getTodos, 
  createTodo, 
  updateTodo, 
  deleteTodo,
  getMessages,
  sendMessage 
} from '../api';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('mock-token');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Authentication', () => {
    describe('login', () => {
      it('should login successfully with valid credentials', async () => {
        const mockResponse = {
          success: true,
          token: 'mock-token',
          user: { id: '1', email: '<EMAIL>', name: 'Test User' }
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        });

        const result = await login('<EMAIL>', 'password');

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/auth/login'),
          expect.objectContaining({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              email: '<EMAIL>',
              password: 'password'
            }),
          })
        );

        expect(result).toEqual(mockResponse);
      });

      it('should handle login failure', async () => {
        const mockError = {
          success: false,
          message: 'Invalid credentials'
        };

        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 401,
          json: () => Promise.resolve(mockError),
        });

        await expect(login('<EMAIL>', 'wrong-password'))
          .rejects.toThrow('Invalid credentials');
      });

      it('should handle network errors', async () => {
        mockFetch.mockRejectedValueOnce(new Error('Network error'));

        await expect(login('<EMAIL>', 'password'))
          .rejects.toThrow('Network error');
      });
    });

    describe('register', () => {
      it('should register successfully with valid data', async () => {
        const mockResponse = {
          success: true,
          message: 'User registered successfully'
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        });

        const userData = {
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123'
        };

        const result = await register(userData);

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/auth/register'),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify(userData),
          })
        );

        expect(result).toEqual(mockResponse);
      });

      it('should handle registration validation errors', async () => {
        const mockError = {
          success: false,
          errors: [
            { field: 'email', message: 'Email already exists' }
          ]
        };

        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: () => Promise.resolve(mockError),
        });

        await expect(register({
          name: 'Test',
          email: '<EMAIL>',
          password: 'password'
        })).rejects.toThrow();
      });
    });
  });

  describe('Todo Operations', () => {
    describe('getTodos', () => {
      it('should fetch todos successfully', async () => {
        const mockTodos = [
          { id: '1', title: 'Test Todo 1', completed: false },
          { id: '2', title: 'Test Todo 2', completed: true }
        ];

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockTodos }),
        });

        const result = await getTodos();

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/todos'),
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Authorization': 'Bearer mock-token'
            })
          })
        );

        expect(result.data).toEqual(mockTodos);
      });

      it('should handle empty todo list', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: [] }),
        });

        const result = await getTodos();
        expect(result.data).toEqual([]);
      });
    });

    describe('createTodo', () => {
      it('should create todo successfully', async () => {
        const newTodo = { title: 'New Todo', description: 'Test description' };
        const mockResponse = {
          success: true,
          data: { id: '3', ...newTodo, completed: false }
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        });

        const result = await createTodo(newTodo);

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/todos'),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify(newTodo),
          })
        );

        expect(result).toEqual(mockResponse);
      });

      it('should handle validation errors', async () => {
        const invalidTodo = { title: '' }; // Empty title

        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: () => Promise.resolve({
            success: false,
            errors: [{ field: 'title', message: 'Title is required' }]
          }),
        });

        await expect(createTodo(invalidTodo)).rejects.toThrow();
      });
    });

    describe('updateTodo', () => {
      it('should update todo successfully', async () => {
        const todoId = '1';
        const updates = { title: 'Updated Todo', completed: true };
        const mockResponse = {
          success: true,
          data: { id: todoId, ...updates }
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        });

        const result = await updateTodo(todoId, updates);

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining(`/todos/${todoId}`),
          expect.objectContaining({
            method: 'PUT',
            body: JSON.stringify(updates),
          })
        );

        expect(result).toEqual(mockResponse);
      });

      it('should handle non-existent todo', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 404,
          json: () => Promise.resolve({
            success: false,
            message: 'Todo not found'
          }),
        });

        await expect(updateTodo('999', { title: 'Updated' }))
          .rejects.toThrow('Todo not found');
      });
    });

    describe('deleteTodo', () => {
      it('should delete todo successfully', async () => {
        const todoId = '1';
        const mockResponse = { success: true, message: 'Todo deleted' };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        });

        const result = await deleteTodo(todoId);

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining(`/todos/${todoId}`),
          expect.objectContaining({
            method: 'DELETE',
          })
        );

        expect(result).toEqual(mockResponse);
      });
    });
  });

  describe('Chat Operations', () => {
    describe('getMessages', () => {
      it('should fetch messages successfully', async () => {
        const groupId = 'group-1';
        const mockMessages = [
          { id: '1', content: 'Hello', sender: { name: 'User 1' } },
          { id: '2', content: 'Hi there', sender: { name: 'User 2' } }
        ];

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockMessages }),
        });

        const result = await getMessages(groupId);

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining(`/groups/${groupId}/messages`),
          expect.objectContaining({
            method: 'GET',
          })
        );

        expect(result.data).toEqual(mockMessages);
      });
    });

    describe('sendMessage', () => {
      it('should send message successfully', async () => {
        const groupId = 'group-1';
        const messageData = { content: 'Hello world' };
        const mockResponse = {
          success: true,
          data: { id: '3', ...messageData, timestamp: Date.now() }
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        });

        const result = await sendMessage(groupId, messageData);

        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining(`/groups/${groupId}/messages`),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify(messageData),
          })
        );

        expect(result).toEqual(mockResponse);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle 401 unauthorized errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({
          success: false,
          message: 'Unauthorized'
        }),
      });

      // Mock window.location
      delete window.location;
      window.location = { href: '' };

      await expect(getTodos()).rejects.toThrow();
      
      // Should redirect to login on 401
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token');
    });

    it('should handle server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.resolve({
          success: false,
          message: 'Internal server error'
        }),
      });

      await expect(getTodos()).rejects.toThrow('Internal server error');
    });

    it('should handle timeout errors', async () => {
      // Mock a timeout scenario
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      await expect(getTodos()).rejects.toThrow('Timeout');
    });
  });

  describe('Caching', () => {
    it('should cache GET requests', async () => {
      const mockResponse = { success: true, data: [] };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      // First call
      await getTodos();
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Second call should use cache (if caching is implemented)
      await getTodos();
      // This test would need to be adjusted based on actual caching implementation
    });

    it('should invalidate cache on mutations', async () => {
      // This would test cache invalidation after POST/PUT/DELETE operations
      // Implementation depends on actual caching strategy
    });
  });
});
