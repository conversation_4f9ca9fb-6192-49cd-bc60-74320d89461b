import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

function OneSignalInit() {
  useEffect(() => {
    if (window.OneSignal) {
      window.OneSignal = window.OneSignal || [];
      window.OneSignal.push(function() {
        window.OneSignal.init({
          appId: 'YOUR_ONESIGNAL_APP_ID', // Thay bằng App ID thật
        });
      });
    }
  }, []);
  return null;
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <OneSignalInit />
    <App />
  </React.StrictMode>
);
