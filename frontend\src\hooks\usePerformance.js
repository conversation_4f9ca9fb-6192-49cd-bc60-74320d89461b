import { useEffect, useRef, useState } from 'react';

/**
 * Performance monitoring hook
 * Tracks component render times, memory usage, and provides optimization insights
 */
export const usePerformance = (componentName = 'Unknown') => {
  const renderStartTime = useRef(Date.now());
  const renderCount = useRef(0);
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    renderCount: 0,
    memoryUsage: 0,
    isSlowRender: false
  });

  useEffect(() => {
    renderCount.current += 1;
    const renderTime = Date.now() - renderStartTime.current;
    const isSlowRender = renderTime > 16; // 60fps = 16ms per frame

    // Get memory usage if available
    const memoryUsage = performance.memory ? {
      used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
    } : null;

    setMetrics({
      renderTime,
      renderCount: renderCount.current,
      memoryUsage,
      isSlowRender
    });

    // Log slow renders in development
    if (process.env.NODE_ENV === 'development' && isSlowRender) {
      console.warn(`🐌 Slow render detected in ${componentName}: ${renderTime}ms`);
    }

    // Reset timer for next render
    renderStartTime.current = Date.now();
  });

  return metrics;
};

/**
 * Hook to measure API call performance
 */
export const useAPIPerformance = () => {
  const [apiMetrics, setApiMetrics] = useState({
    totalCalls: 0,
    averageResponseTime: 0,
    slowCalls: 0,
    errorRate: 0,
    cacheHitRate: 0
  });

  const trackAPICall = (startTime, endTime, success = true, cached = false) => {
    const responseTime = endTime - startTime;
    
    setApiMetrics(prev => {
      const newTotalCalls = prev.totalCalls + 1;
      const newSlowCalls = responseTime > 1000 ? prev.slowCalls + 1 : prev.slowCalls;
      const newErrorRate = success ? prev.errorRate : prev.errorRate + 1;
      const newCacheHits = cached ? prev.cacheHitRate + 1 : prev.cacheHitRate;
      
      return {
        totalCalls: newTotalCalls,
        averageResponseTime: ((prev.averageResponseTime * prev.totalCalls) + responseTime) / newTotalCalls,
        slowCalls: newSlowCalls,
        errorRate: (newErrorRate / newTotalCalls) * 100,
        cacheHitRate: (newCacheHits / newTotalCalls) * 100
      };
    });

    // Log slow API calls
    if (process.env.NODE_ENV === 'development' && responseTime > 1000) {
      console.warn(`🐌 Slow API call: ${responseTime}ms`);
    }
  };

  return { apiMetrics, trackAPICall };
};

/**
 * Hook to monitor bundle size and loading performance
 */
export const useBundlePerformance = () => {
  const [bundleMetrics, setBundleMetrics] = useState({
    loadTime: 0,
    domContentLoaded: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0
  });

  useEffect(() => {
    // Get navigation timing
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      setBundleMetrics(prev => ({
        ...prev,
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
      }));
    }

    // Get paint timing
    const paintEntries = performance.getEntriesByType('paint');
    paintEntries.forEach(entry => {
      if (entry.name === 'first-contentful-paint') {
        setBundleMetrics(prev => ({
          ...prev,
          firstContentfulPaint: entry.startTime
        }));
      }
    });

    // Get LCP if available
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        setBundleMetrics(prev => ({
          ...prev,
          largestContentfulPaint: lastEntry.startTime
        }));
      });

      try {
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        // LCP not supported
      }

      // Get CLS if available
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        setBundleMetrics(prev => ({
          ...prev,
          cumulativeLayoutShift: clsValue
        }));
      });

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        // CLS not supported
      }

      return () => {
        observer.disconnect();
        clsObserver.disconnect();
      };
    }
  }, []);

  return bundleMetrics;
};

/**
 * Hook to optimize images and lazy loading
 */
export const useImageOptimization = () => {
  const [imageMetrics, setImageMetrics] = useState({
    totalImages: 0,
    loadedImages: 0,
    failedImages: 0,
    averageLoadTime: 0
  });

  const trackImageLoad = (startTime, success = true) => {
    const loadTime = Date.now() - startTime;
    
    setImageMetrics(prev => {
      const newTotal = prev.totalImages + 1;
      const newLoaded = success ? prev.loadedImages + 1 : prev.loadedImages;
      const newFailed = success ? prev.failedImages : prev.failedImages + 1;
      
      return {
        totalImages: newTotal,
        loadedImages: newLoaded,
        failedImages: newFailed,
        averageLoadTime: success ? 
          ((prev.averageLoadTime * prev.loadedImages) + loadTime) / newLoaded :
          prev.averageLoadTime
      };
    });
  };

  return { imageMetrics, trackImageLoad };
};

/**
 * Main performance dashboard hook
 */
export const usePerformanceDashboard = () => {
  const bundleMetrics = useBundlePerformance();
  const { apiMetrics } = useAPIPerformance();
  const { imageMetrics } = useImageOptimization();

  const getPerformanceScore = () => {
    let score = 100;
    
    // Deduct points for slow metrics
    if (bundleMetrics.firstContentfulPaint > 2000) score -= 20;
    if (bundleMetrics.largestContentfulPaint > 4000) score -= 20;
    if (bundleMetrics.cumulativeLayoutShift > 0.1) score -= 15;
    if (apiMetrics.averageResponseTime > 500) score -= 15;
    if (apiMetrics.errorRate > 5) score -= 10;
    if (imageMetrics.averageLoadTime > 1000) score -= 10;
    if (apiMetrics.cacheHitRate < 30) score -= 10;

    return Math.max(0, score);
  };

  const getRecommendations = () => {
    const recommendations = [];
    
    if (bundleMetrics.firstContentfulPaint > 2000) {
      recommendations.push('Consider code splitting and lazy loading to improve First Contentful Paint');
    }
    if (bundleMetrics.largestContentfulPaint > 4000) {
      recommendations.push('Optimize images and critical resources for better Largest Contentful Paint');
    }
    if (bundleMetrics.cumulativeLayoutShift > 0.1) {
      recommendations.push('Add size attributes to images and avoid dynamic content insertion');
    }
    if (apiMetrics.averageResponseTime > 500) {
      recommendations.push('Optimize API responses and consider implementing caching');
    }
    if (apiMetrics.cacheHitRate < 30) {
      recommendations.push('Implement better caching strategies for API calls');
    }
    if (imageMetrics.averageLoadTime > 1000) {
      recommendations.push('Optimize image sizes and implement lazy loading');
    }

    return recommendations;
  };

  return {
    bundleMetrics,
    apiMetrics,
    imageMetrics,
    performanceScore: getPerformanceScore(),
    recommendations: getRecommendations()
  };
};

export default {
  usePerformance,
  useAPIPerformance,
  useBundlePerformance,
  useImageOptimization,
  usePerformanceDashboard
};
