const fs = require('fs');
const path = require('path');

// Files to update
const filesToUpdate = [
  'src/components/AdvancedChatbot.jsx',
  'src/components/TypewriterEffect.jsx',
  'src/pages/CalendarSync.jsx',
  'src/pages/Features.jsx',
  'src/pages/FileManager.jsx',
  'src/pages/Forum.jsx',
  'src/pages/Group.jsx',
  'src/pages/Home_old.jsx',
  'src/pages/Leaderboard.jsx',
  'src/pages/Login_old.jsx',
  'src/pages/Notification.jsx',
  'src/pages/Register.jsx',
  'src/pages/Settings.jsx',
  'src/pages/Sidebar.jsx',
  'src/pages/Summary.jsx',
  'src/pages/ToDo.jsx'
];

// Function to replace Chakra UI imports
function replaceChakraImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace Chakra UI import with ChakraToMui import
    content = content.replace(
      /import\s*{([^}]+)}\s*from\s*['"]@chakra-ui\/react['"];?/g,
      (match, imports) => {
        return `import {\n${imports}\n} from '../components/ChakraToMui';`;
      }
    );
    
    // Also replace any ChakraReplacements imports
    content = content.replace(
      /import\s*{([^}]+)}\s*from\s*['"]\.\.\/components\/ChakraReplacements['"];?/g,
      (match, imports) => {
        return `import {\n${imports}\n} from '../components/ChakraToMui';`;
      }
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
}

// Update all files
console.log('🔄 Replacing Chakra UI imports with ChakraToMui...\n');

filesToUpdate.forEach(file => {
  const fullPath = path.join(__dirname, file);
  if (fs.existsSync(fullPath)) {
    replaceChakraImports(fullPath);
  } else {
    console.log(`⚠️  File not found: ${fullPath}`);
  }
});

console.log('\n✅ All imports have been updated!');
console.log('🚀 You can now run the frontend without Chakra UI errors.');
