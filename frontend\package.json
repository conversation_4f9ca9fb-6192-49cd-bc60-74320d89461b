{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/x-date-pickers": "^8.6.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^7.0.0"}}