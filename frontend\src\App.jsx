import React, { createContext, useContext, useEffect, useState, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { MuiProvider } from './components/MuiProvider';
import { ToastProvider } from './components/ToastProvider';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingAnimation from './components/LoadingAnimation';

// Lazy load components for better performance
const Home = React.lazy(() => import('./pages/Home'));
const Login = React.lazy(() => import('./pages/Login'));
const Register = React.lazy(() => import('./pages/Register'));
const ToDo = React.lazy(() => import('./pages/ToDo'));
const Group = React.lazy(() => import('./pages/Group'));
const Chat = React.lazy(() => import('./pages/Chat'));
const Trial = React.lazy(() => import('./pages/Trial'));
const Chatbot = React.lazy(() => import('./pages/Chatbot'));
const Leaderboard = React.lazy(() => import('./pages/Leaderboard'));
const Forum = React.lazy(() => import('./pages/Forum'));
const CalendarSync = React.lazy(() => import('./pages/CalendarSync'));
const FileManager = React.lazy(() => import('./pages/FileManager'));
const NotionWorkspace = React.lazy(() => import('./pages/NotionWorkspace'));
const Navigation = React.lazy(() => import('./pages/Navigation'));
const Event = React.lazy(() => import('./pages/Event'));
const Exam = React.lazy(() => import('./pages/Exam'));
const Resource = React.lazy(() => import('./pages/Resource'));
const Mentor = React.lazy(() => import('./pages/Mentor'));
const Feedback = React.lazy(() => import('./pages/Feedback'));
const Progress = React.lazy(() => import('./pages/Progress'));
const Notification = React.lazy(() => import('./pages/Notification'));
const Demo = React.lazy(() => import('./pages/Demo'));
const Features = React.lazy(() => import('./pages/Features'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Settings = React.lazy(() => import('./pages/Settings'));
const MultiAgentDashboard = React.lazy(() => import('./pages/MultiAgentDashboard'));

// Auth Context
const AuthContext = createContext();

export function useAuth() { 
  return useContext(AuthContext); 
}

// Protected Route Component
function ProtectedRoute({ children }) {
  const { user } = useAuth();
  return user ? children : <Navigate to="/login" />;
}

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUser({ name: payload.name || 'User', ...payload });
      } catch {
        setUser(null);
      }
    } else {
      setUser(null);
    }
    setLoading(false);
  }, []);

  const login = (userData, token) => {
    setUser(userData);
    localStorage.setItem('token', token);
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('token');
  };

  if (loading) {
    return (
      <MuiProvider>
        <LoadingAnimation
          variant="thinking"
          message="Đang khởi tạo ứng dụng..."
          fullScreen
        />
      </MuiProvider>
    );
  }

  return (
    <ErrorBoundary>
      <MuiProvider>
        <AuthContext.Provider value={{ user, login, logout }}>
          <Router>
            <ToastProvider>
              <div style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
                {user && <Navigation />}

                <main style={{ paddingTop: user ? '80px' : '0' }}>
                  <ErrorBoundary>
                    <Suspense fallback={
                      <LoadingAnimation
                        variant="thinking"
                        message="Đang tải trang..."
                        fullScreen
                      />
                    }>
                      <Routes>
                      <Route path="/" element={<Home />} />
                      <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
                      <Route path="/multi-agent" element={<ProtectedRoute><MultiAgentDashboard /></ProtectedRoute>} />
                      <Route path="/demo" element={<Demo />} />
                      <Route path="/features" element={<Features />} />
                      <Route path="/event" element={<Event />} />
                      <Route path="/todo" element={<ProtectedRoute><ToDo /></ProtectedRoute>} />
                      <Route path="/group" element={<ProtectedRoute><Group /></ProtectedRoute>} />
                      <Route path="/chat" element={<ProtectedRoute><Chat /></ProtectedRoute>} />
                      <Route path="/trial" element={<Trial />} />
                      <Route path="/chatbot" element={<Chatbot />} />
                      <Route path="/leaderboard" element={<Leaderboard />} />
                      <Route path="/forum" element={<Forum />} />
                      <Route path="/exam" element={<ProtectedRoute><Exam /></ProtectedRoute>} />
                      <Route path="/resource" element={<ProtectedRoute><Resource /></ProtectedRoute>} />
                      <Route path="/mentor" element={<ProtectedRoute><Mentor /></ProtectedRoute>} />
                      <Route path="/feedback" element={<ProtectedRoute><Feedback /></ProtectedRoute>} />
                      <Route path="/progress" element={<ProtectedRoute><Progress /></ProtectedRoute>} />
                      <Route path="/notification" element={<ProtectedRoute><Notification /></ProtectedRoute>} />
                      <Route path="/calendar-sync" element={<CalendarSync />} />
                      <Route path="/file-manager" element={<FileManager />} />
              <Route path="/workspace" element={<NotionWorkspace />} />
                      <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
                      <Route path="/login" element={<Login />} />
                      <Route path="/register" element={<Register />} />
                    </Routes>
                    </Suspense>
                  </ErrorBoundary>
                </main>
              </div>
            </ToastProvider>
          </Router>
        </AuthContext.Provider>
      </MuiProvider>
    </ErrorBoundary>
  );
}

export default App;
