const { body, param, query, validationResult } = require('express-validator');
const xss = require('xss');
const rateLimit = require('express-rate-limit');

// Enhanced XSS configuration
const xssOptions = {
  whiteList: {
    // Allow basic formatting tags
    b: [],
    i: [],
    em: [],
    strong: [],
    br: [],
    p: [],
    // Add more as needed but be restrictive
  },
  stripIgnoreTag: true,
  stripIgnoreTagBody: ['script', 'style'],
  allowCommentTag: false,
  css: false // Disable CSS to prevent CSS injection
};

// Enhanced input sanitization with logging
const sanitizeInput = (req, res, next) => {
  const originalData = {};
  let hasChanges = false;

  const sanitizeObject = (obj, path = '') => {
    for (let key in obj) {
      const currentPath = path ? `${path}.${key}` : key;

      if (typeof obj[key] === 'string') {
        const original = obj[key];
        const sanitized = xss(obj[key], xssOptions);

        if (original !== sanitized) {
          hasChanges = true;
          originalData[currentPath] = original;
          console.warn('XSS attempt detected and sanitized:', {
            path: currentPath,
            original: original.substring(0, 100), // Log first 100 chars
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            timestamp: new Date().toISOString()
          });
        }

        obj[key] = sanitized;
      } else if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        sanitizeObject(obj[key], currentPath);
      } else if (Array.isArray(obj[key])) {
        obj[key].forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            sanitizeObject(item, `${currentPath}[${index}]`);
          } else if (typeof item === 'string') {
            const original = item;
            const sanitized = xss(item, xssOptions);
            if (original !== sanitized) {
              hasChanges = true;
              originalData[`${currentPath}[${index}]`] = original;
              obj[key][index] = sanitized;
            }
          }
        });
      }
    }
  };

  if (req.body) sanitizeObject(req.body, 'body');
  if (req.query) sanitizeObject(req.query, 'query');
  if (req.params) sanitizeObject(req.params, 'params');

  // Log if any XSS attempts were detected
  if (hasChanges) {
    req.xssDetected = true;
    req.originalData = originalData;
  }

  next();
};

// Enhanced validation result checking with detailed logging
const checkValidation = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorDetails = errors.array();

    // Log validation failures for security monitoring
    console.warn('Validation failed:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      method: req.method,
      errors: errorDetails,
      timestamp: new Date().toISOString(),
      userId: req.user?.id || 'anonymous'
    });

    // Group errors by field for better UX
    const groupedErrors = errorDetails.reduce((acc, error) => {
      const field = error.path || error.param;
      if (!acc[field]) {
        acc[field] = [];
      }
      acc[field].push(error.msg);
      return acc;
    }, {});

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errorDetails,
      groupedErrors,
      code: 'VALIDATION_ERROR'
    });
  }

  next();
};

// Rate limiting for sensitive endpoints
const createRateLimit = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Custom key generator to include user ID if available
    keyGenerator: (req) => {
      return req.user?.id ? `${req.ip}-${req.user.id}` : req.ip;
    },
    // Skip successful requests in some cases
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    // Custom handler for rate limit exceeded
    handler: (req, res) => {
      console.warn('Rate limit exceeded:', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        userId: req.user?.id || 'anonymous',
        timestamp: new Date().toISOString()
      });

      res.status(429).json(options.message || defaultOptions.message);
    }
  };

  return rateLimit({ ...defaultOptions, ...options });
};

// Specific rate limiters for different endpoints
const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per 15 minutes
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
    code: 'AUTH_RATE_LIMIT_EXCEEDED'
  }
});

const apiRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000,
  max: 200, // 200 requests per 15 minutes
  skipSuccessfulRequests: true
});

const uploadRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 uploads per hour
  message: {
    success: false,
    message: 'Too many file uploads, please try again later.',
    code: 'UPLOAD_RATE_LIMIT_EXCEEDED'
  }
});

// Common validation rules
const validationRules = {
  // User validation
  registerUser: [
    body('name')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Name must be between 2 and 50 characters')
      .matches(/^[a-zA-Z\s\u00C0-\u017F]+$/)
      .withMessage('Name can only contain letters and spaces'),
    
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long')
  ],

  loginUser: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ],

  // Todo validation
  createTodo: [
    body('title')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Title must be between 1 and 200 characters'),
    
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description cannot exceed 1000 characters'),
    
    body('priority')
      .optional()
      .isIn(['low', 'medium', 'high'])
      .withMessage('Priority must be low, medium, or high'),
    
    body('dueDate')
      .optional()
      .isISO8601()
      .withMessage('Due date must be a valid date')
  ],

  // Group validation
  createGroup: [
    body('name')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Group name must be between 2 and 100 characters'),
    
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description cannot exceed 500 characters')
  ],

  // Post validation
  createPost: [
    body('title')
      .trim()
      .isLength({ min: 5, max: 200 })
      .withMessage('Title must be between 5 and 200 characters'),
    
    body('content')
      .trim()
      .isLength({ min: 10, max: 5000 })
      .withMessage('Content must be between 10 and 5000 characters'),
    
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array'),
    
    body('tags.*')
      .optional()
      .trim()
      .isLength({ min: 1, max: 30 })
      .withMessage('Each tag must be between 1 and 30 characters')
  ],

  // Comment validation
  createComment: [
    body('content')
      .trim()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Comment must be between 1 and 1000 characters')
  ],

  // MongoDB ObjectId validation
  validateObjectId: [
    param('id')
      .isMongoId()
      .withMessage('Invalid ID format')
  ],

  // Pagination validation
  validatePagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ]
};

// Input size validation middleware
const validateInputSize = (maxSize = 1024 * 1024) => { // Default 1MB
  return (req, res, next) => {
    const contentLength = parseInt(req.headers['content-length']);

    if (contentLength && contentLength > maxSize) {
      console.warn('Request size exceeded:', {
        ip: req.ip,
        contentLength,
        maxSize,
        endpoint: req.originalUrl,
        timestamp: new Date().toISOString()
      });

      return res.status(413).json({
        success: false,
        message: 'Request entity too large',
        code: 'REQUEST_TOO_LARGE',
        maxSize
      });
    }

    next();
  };
};

// SQL injection prevention (for any raw queries)
const preventSQLInjection = (req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(--|\/\*|\*\/|;|'|"|`)/g,
    /(\bOR\b|\bAND\b).*?(\b=\b|\bLIKE\b)/gi
  ];

  const checkForSQL = (obj, path = '') => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of sqlPatterns) {
          if (pattern.test(obj[key])) {
            console.error('SQL injection attempt detected:', {
              ip: req.ip,
              userAgent: req.get('User-Agent'),
              path: path ? `${path}.${key}` : key,
              value: obj[key].substring(0, 100),
              timestamp: new Date().toISOString()
            });

            return res.status(400).json({
              success: false,
              message: 'Invalid input detected',
              code: 'INVALID_INPUT'
            });
          }
        }
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        const result = checkForSQL(obj[key], path ? `${path}.${key}` : key);
        if (result) return result;
      }
    }
  };

  if (req.body) {
    const result = checkForSQL(req.body, 'body');
    if (result) return result;
  }
  if (req.query) {
    const result = checkForSQL(req.query, 'query');
    if (result) return result;
  }
  if (req.params) {
    const result = checkForSQL(req.params, 'params');
    if (result) return result;
  }

  next();
};

module.exports = {
  sanitizeInput,
  checkValidation,
  validationRules,
  createRateLimit,
  authRateLimit,
  apiRateLimit,
  uploadRateLimit,
  validateInputSize,
  preventSQLInjection
};
