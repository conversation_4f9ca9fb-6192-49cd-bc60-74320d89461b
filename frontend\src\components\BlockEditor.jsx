import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Stack,
  Chip,
  Checkbox,
  Button,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  DragIndicator as DragIcon,
  MoreVert as MoreIcon,
  Title as HeadingIcon,
  FormatQuote as QuoteIcon,
  Code as CodeIcon,
  CheckBox as CheckboxIcon,
  List as ListIcon,
  Image as ImageIcon,
  TableChart as TableIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon
} from '@mui/icons-material';

const BLOCK_TYPES = {
  TEXT: 'text',
  HEADING: 'heading',
  QUOTE: 'quote',
  CODE: 'code',
  CHECKLIST: 'checklist',
  BULLET_LIST: 'bullet_list',
  NUMBERED_LIST: 'numbered_list',
  DIVIDER: 'divider',
  CALLOUT: 'callout',
  TOGGLE: 'toggle',
  IMAGE: 'image',
  TABLE: 'table'
};

const CALLOUT_TYPES = {
  INFO: { icon: <InfoIcon />, color: 'info', label: 'Thông tin' },
  WARNING: { icon: <WarningIcon />, color: 'warning', label: 'Cảnh báo' },
  ERROR: { icon: <ErrorIcon />, color: 'error', label: 'Lỗi' },
  SUCCESS: { icon: <SuccessIcon />, color: 'success', label: 'Thành công' }
};

const BlockEditor = ({ blocks = [], onChange, placeholder = "Nhập '/' để thêm block..." }) => {
  const [localBlocks, setLocalBlocks] = useState(blocks);
  const [focusedBlock, setFocusedBlock] = useState(null);
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const [currentBlockIndex, setCurrentBlockIndex] = useState(0);

  useEffect(() => {
    setLocalBlocks(blocks);
  }, [blocks]);

  const handleBlockChange = (index, newContent) => {
    const updatedBlocks = [...localBlocks];
    updatedBlocks[index] = { ...updatedBlocks[index], content: newContent };
    setLocalBlocks(updatedBlocks);
    onChange?.(updatedBlocks);
  };

  const handleBlockTypeChange = (index, newType) => {
    const updatedBlocks = [...localBlocks];
    updatedBlocks[index] = { 
      ...updatedBlocks[index], 
      type: newType,
      content: newType === BLOCK_TYPES.CHECKLIST ? [{ text: '', checked: false }] : ''
    };
    setLocalBlocks(updatedBlocks);
    onChange?.(updatedBlocks);
    setMenuAnchor(null);
  };

  const addBlock = (index, type = BLOCK_TYPES.TEXT) => {
    const newBlock = {
      id: Date.now() + Math.random(),
      type,
      content: type === BLOCK_TYPES.CHECKLIST ? [{ text: '', checked: false }] : '',
      metadata: {}
    };

    const updatedBlocks = [...localBlocks];
    updatedBlocks.splice(index + 1, 0, newBlock);
    setLocalBlocks(updatedBlocks);
    onChange?.(updatedBlocks);
  };

  const deleteBlock = (index) => {
    if (localBlocks.length <= 1) return;
    const updatedBlocks = localBlocks.filter((_, i) => i !== index);
    setLocalBlocks(updatedBlocks);
    onChange?.(updatedBlocks);
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      addBlock(index);
    } else if (e.key === 'Backspace' && e.target.value === '' && localBlocks.length > 1) {
      e.preventDefault();
      deleteBlock(index);
      // Focus previous block
      if (index > 0) {
        setTimeout(() => setFocusedBlock(index - 1), 0);
      }
    } else if (e.key === '/' && e.target.value === '') {
      e.preventDefault();
      setCurrentBlockIndex(index);
      setMenuPosition({ x: e.clientX, y: e.clientY });
      setMenuAnchor(e.target);
    }
  };

  const renderBlock = (block, index) => {
    const isLast = index === localBlocks.length - 1;

    switch (block.type) {
      case BLOCK_TYPES.HEADING:
        return (
          <TextField
            fullWidth
            variant="standard"
            placeholder="Tiêu đề"
            value={block.content}
            onChange={(e) => handleBlockChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            sx={{
              '& .MuiInput-input': {
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: 'primary.main'
              }
            }}
          />
        );

      case BLOCK_TYPES.QUOTE:
        return (
          <Box sx={{ borderLeft: 4, borderColor: 'grey.400', pl: 2, bgcolor: 'grey.50', py: 1 }}>
            <TextField
              fullWidth
              variant="standard"
              placeholder="Trích dẫn..."
              value={block.content}
              onChange={(e) => handleBlockChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              sx={{
                '& .MuiInput-input': {
                  fontStyle: 'italic',
                  color: 'text.secondary'
                }
              }}
            />
          </Box>
        );

      case BLOCK_TYPES.CODE:
        return (
          <Paper elevation={1} sx={{ bgcolor: 'grey.900', p: 2, borderRadius: 1 }}>
            <TextField
              fullWidth
              multiline
              variant="standard"
              placeholder="// Code..."
              value={block.content}
              onChange={(e) => handleBlockChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              sx={{
                '& .MuiInput-input': {
                  fontFamily: 'monospace',
                  color: 'white',
                  fontSize: '0.9rem'
                },
                '& .MuiInput-root:before': { borderBottom: 'none' },
                '& .MuiInput-root:after': { borderBottom: 'none' },
                '& .MuiInput-root:hover:not(.Mui-disabled):before': { borderBottom: 'none' }
              }}
            />
          </Paper>
        );

      case BLOCK_TYPES.CHECKLIST:
        return (
          <Stack spacing={1}>
            {(Array.isArray(block.content) ? block.content : [{ text: '', checked: false }]).map((item, itemIndex) => (
              <Stack key={itemIndex} direction="row" alignItems="center" spacing={1}>
                <Checkbox
                  checked={item.checked}
                  onChange={(e) => {
                    const newContent = [...block.content];
                    newContent[itemIndex] = { ...item, checked: e.target.checked };
                    handleBlockChange(index, newContent);
                  }}
                  size="small"
                />
                <TextField
                  fullWidth
                  variant="standard"
                  placeholder="Mục checklist..."
                  value={item.text}
                  onChange={(e) => {
                    const newContent = [...block.content];
                    newContent[itemIndex] = { ...item, text: e.target.value };
                    handleBlockChange(index, newContent);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const newContent = [...block.content];
                      newContent.splice(itemIndex + 1, 0, { text: '', checked: false });
                      handleBlockChange(index, newContent);
                    }
                  }}
                  sx={{
                    '& .MuiInput-input': {
                      textDecoration: item.checked ? 'line-through' : 'none',
                      color: item.checked ? 'text.secondary' : 'text.primary'
                    }
                  }}
                />
              </Stack>
            ))}
          </Stack>
        );

      case BLOCK_TYPES.CALLOUT:
        const calloutType = CALLOUT_TYPES[block.metadata?.calloutType || 'INFO'];
        return (
          <Alert 
            severity={calloutType.color} 
            icon={calloutType.icon}
            sx={{ '& .MuiAlert-message': { width: '100%' } }}
          >
            <TextField
              fullWidth
              variant="standard"
              placeholder={`${calloutType.label}...`}
              value={block.content}
              onChange={(e) => handleBlockChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              sx={{
                '& .MuiInput-root:before': { borderBottom: 'none' },
                '& .MuiInput-root:after': { borderBottom: 'none' },
                '& .MuiInput-root:hover:not(.Mui-disabled):before': { borderBottom: 'none' }
              }}
            />
          </Alert>
        );

      case BLOCK_TYPES.TOGGLE:
        return (
          <Accordion elevation={0} sx={{ border: 1, borderColor: 'divider' }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <TextField
                variant="standard"
                placeholder="Toggle title..."
                value={block.content}
                onChange={(e) => handleBlockChange(index, e.target.value)}
                onClick={(e) => e.stopPropagation()}
                sx={{ flexGrow: 1, mr: 2 }}
              />
            </AccordionSummary>
            <AccordionDetails>
              <TextField
                fullWidth
                multiline
                variant="standard"
                placeholder="Toggle content..."
                value={block.metadata?.toggleContent || ''}
                onChange={(e) => {
                  const updatedBlocks = [...localBlocks];
                  updatedBlocks[index] = {
                    ...updatedBlocks[index],
                    metadata: { ...updatedBlocks[index].metadata, toggleContent: e.target.value }
                  };
                  setLocalBlocks(updatedBlocks);
                  onChange?.(updatedBlocks);
                }}
              />
            </AccordionDetails>
          </Accordion>
        );

      case BLOCK_TYPES.DIVIDER:
        return <Divider sx={{ my: 2 }} />;

      default: // TEXT
        return (
          <TextField
            fullWidth
            variant="standard"
            placeholder={isLast ? placeholder : "Nhập văn bản..."}
            value={block.content}
            onChange={(e) => handleBlockChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            multiline
          />
        );
    }
  };

  return (
    <Box>
      {localBlocks.map((block, index) => (
        <Box
          key={block.id || index}
          sx={{
            position: 'relative',
            mb: 2,
            '&:hover .block-controls': { opacity: 1 }
          }}
        >
          {/* Block Controls */}
          <Stack
            direction="row"
            className="block-controls"
            sx={{
              position: 'absolute',
              left: -40,
              top: 0,
              opacity: 0,
              transition: 'opacity 0.2s'
            }}
          >
            <IconButton size="small" sx={{ cursor: 'grab' }}>
              <DragIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => addBlock(index)}
            >
              <AddIcon fontSize="small" />
            </IconButton>
          </Stack>

          {/* Block Content */}
          {renderBlock(block, index)}
        </Box>
      ))}

      {/* Block Type Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
        anchorReference="anchorPosition"
        anchorPosition={{ top: menuPosition.y, left: menuPosition.x }}
      >
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.TEXT)}>
          📝 Văn bản
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.HEADING)}>
          <HeadingIcon sx={{ mr: 1 }} /> Tiêu đề
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.QUOTE)}>
          <QuoteIcon sx={{ mr: 1 }} /> Trích dẫn
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.CODE)}>
          <CodeIcon sx={{ mr: 1 }} /> Code
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.CHECKLIST)}>
          <CheckboxIcon sx={{ mr: 1 }} /> Checklist
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.CALLOUT)}>
          <InfoIcon sx={{ mr: 1 }} /> Callout
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.TOGGLE)}>
          <ExpandMoreIcon sx={{ mr: 1 }} /> Toggle
        </MenuItem>
        <MenuItem onClick={() => handleBlockTypeChange(currentBlockIndex, BLOCK_TYPES.DIVIDER)}>
          ➖ Divider
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default BlockEditor;
