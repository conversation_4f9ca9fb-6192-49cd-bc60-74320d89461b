require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const { dbManager } = require('./utils/database');
const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const User = require('./models/User');
const jwt = require('jsonwebtoken');
const http = require('http');
const path = require('path');
const { Server } = require('socket.io');
const { sendPushNotification } = require('./utils/notify');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const { errorHandler, notFound } = require('./middleware/errorHandler');
const {
  sanitizeInput,
  apiRateLimit,
  validateInputSize,
  preventSQLInjection
} = require('./middleware/validation');
const { securityHeaders } = require('./middleware/auth');

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Enhanced security headers
app.use(securityHeaders);

// Enhanced Helmet configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://fonts.gstatic.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'", "'unsafe-inline'"], // Consider removing unsafe-inline in production
      connectSrc: ["'self'", "https://api.gemini.google.com", "wss:", "ws:"],
      mediaSrc: ["'self'", "data:", "blob:"],
      objectSrc: ["'none'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"]
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for Socket.IO compatibility
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Enhanced CORS configuration
const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:3000',
  'https://fptunihub.vercel.app'
];

// Add environment-specific origins
if (process.env.NODE_ENV === 'development') {
  allowedOrigins.push('http://localhost:5174', 'http://127.0.0.1:5173');
}

app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    console.warn('CORS blocked origin:', origin);
    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400 // 24 hours
}));

// Input size validation
app.use(validateInputSize(10 * 1024 * 1024)); // 10MB limit

// Body parsing middleware with enhanced security
app.use(express.json({
  limit: '10mb',
  strict: true,
  type: ['application/json', 'application/*+json']
}));
app.use(express.urlencoded({
  extended: true,
  limit: '10mb',
  parameterLimit: 1000 // Limit number of parameters
}));

// SQL injection prevention
app.use(preventSQLInjection);

// Input sanitization (after parsing)
app.use(sanitizeInput);

// Global rate limiting
app.use(apiRateLimit);

// Security logging middleware
app.use((req, res, next) => {
  // Log sensitive endpoints
  const sensitiveEndpoints = ['/api/auth', '/api/admin', '/api/user'];
  const isSensitive = sensitiveEndpoints.some(endpoint => req.path.startsWith(endpoint));

  if (isSensitive || req.method !== 'GET') {
    console.log('Security log:', {
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      userId: req.user?.id || 'anonymous'
    });
  }

  next();
});

// Environment validation
const validateEnvironment = () => {
  const requiredEnvVars = ['MONGO_URI', 'JWT_SECRET'];
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing);
    process.exit(1);
  }

  // Warn about development settings in production
  if (process.env.NODE_ENV === 'production') {
    if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
      console.warn('⚠️ JWT_SECRET should be longer in production');
    }
  }
};

validateEnvironment();

// Enhanced MongoDB connection using DatabaseManager
const initializeDatabase = async () => {
  try {
    const mongoURI = process.env.MONGO_URI;
    if (!mongoURI) {
      throw new Error('MONGO_URI environment variable is not defined');
    }

    // Connect with enhanced configuration
    await dbManager.connect(mongoURI, {
      maxPoolSize: process.env.NODE_ENV === 'production' ? 20 : 10,
      minPoolSize: process.env.NODE_ENV === 'production' ? 5 : 2,
    });

    // Run database maintenance on startup
    if (process.env.NODE_ENV === 'production') {
      await dbManager.runMaintenance();
    }

    console.log('🚀 Database initialization completed');
  } catch (error) {
    console.error('💀 Database initialization failed:', error.message);
    process.exit(1);
  }
};

// Initialize database
initializeDatabase();

// Health check routes
app.get('/', (req, res) => {
  res.json({
    message: 'FPT UniHub Backend is running!',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

app.get('/health', (req, res) => {
  const dbMetrics = dbManager.getMetrics();

  res.json({
    status: dbMetrics.isConnected ? 'healthy' : 'unhealthy',
    database: dbMetrics,
    server: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      nodeVersion: process.version,
      platform: process.platform
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/health/database', (req, res) => {
  const metrics = dbManager.getMetrics();
  const slowQueries = dbManager.getSlowQueries(5);

  res.json({
    metrics,
    slowQueries,
    timestamp: new Date().toISOString()
  });
});

app.use('/api/auth', require('./routes/auth'));
app.use('/api/todo', require('./routes/todo'));
app.use('/api/group', require('./routes/group'));
app.use('/api/thread', require('./routes/thread'));
app.use('/api/ai', require('./routes/ai_rag'));
app.use('/api/leaderboard', require('./routes/leaderboard'));
app.use('/api/forum', require('./routes/forum'));
app.use('/api/calendar', require('./routes/calendar'));
app.use('/api/file', require('./routes/file'));
app.use('/api/event', require('./routes/event'));
app.use('/api/exam', require('./routes/exam'));
app.use('/api/resource', require('./routes/resource'));
app.use('/api/mentor', require('./routes/mentor'));
app.use('/api/feedback', require('./routes/feedback'));
app.use('/api/progress', require('./routes/progress'));
app.use('/api/notification', require('./routes/notification'));
app.use('/api/orchestrator', require('./routes/orchestrator'));
app.use('/api/prompt-enhancer', require('./routes/prompt_enhancer'));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

app.use(passport.initialize());

passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: '/api/auth/google/callback',
}, async (accessToken, refreshToken, profile, done) => {
  try {
    let user = await User.findOne({ googleId: profile.id });
    if (!user) {
      user = await User.create({
        name: profile.displayName,
        email: profile.emails[0].value,
        googleId: profile.id,
        avatar: profile.photos[0].value,
      });
    }
    return done(null, user);
  } catch (err) {
    return done(err, null);
  }
}));

app.get('/api/auth/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

app.get('/api/auth/google/callback', passport.authenticate('google', { session: false }), (req, res) => {
  const token = jwt.sign({ id: req.user._id, name: req.user.name, email: req.user.email }, process.env.JWT_SECRET, { expiresIn: '7d' });
  // Trả về token cho frontend (có thể redirect kèm token hoặc trả JSON)
  res.redirect(`http://localhost:5173/login?token=${token}`);
});

const server = http.createServer(app);
const io = new Server(server, { cors: { origin: '*' } });

// Socket.io chat nhóm
io.on('connection', (socket) => {
  socket.on('joinGroup', (groupId) => {
    socket.join(groupId);
  });
  socket.on('leaveGroup', (groupId) => {
    socket.leave(groupId);
  });
  socket.on('sendMessage', async ({ groupId, message }) => {
    io.to(groupId).emit('newMessage', message);
    // Gửi push notification tới các user trong nhóm (giả lập userIds)
    if (message && message.content) {
      await sendPushNotification({
        title: 'Tin nhắn mới trong nhóm',
        message: message.content,
        userIds: groupId ? [groupId] : [], // Cần thay bằng danh sách userIds thực tế
      });
    }
  });
});

// Error handling middleware (must be last)
app.use(notFound);
app.use(errorHandler);

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
}); 