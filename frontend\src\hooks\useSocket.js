import { useState, useEffect, useCallback, useRef } from 'react';
import socketService from '../services/SocketService';

/**
 * Main socket hook for connection management
 */
export const useSocket = (autoConnect = true) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const reconnectTimeoutRef = useRef(null);

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Update connection status
    const updateStatus = () => {
      const status = socketService.getStatus();
      setIsConnected(status.isConnected);
      setMetrics(socketService.getMetrics());
    };

    const interval = setInterval(updateStatus, 1000);
    updateStatus();

    return () => {
      clearInterval(interval);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [autoConnect]);

  const connect = useCallback(async () => {
    try {
      setConnectionError(null);
      await socketService.connect();
      setIsConnected(true);
    } catch (error) {
      setConnectionError(error.message);
      setIsConnected(false);
      
      // Auto-retry connection after delay
      reconnectTimeoutRef.current = setTimeout(() => {
        connect();
      }, 5000);
    }
  }, []);

  const disconnect = useCallback(() => {
    socketService.disconnect();
    setIsConnected(false);
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, []);

  const emit = useCallback(async (eventName, data, options) => {
    try {
      return await socketService.emit(eventName, data, options);
    } catch (error) {
      console.error('Socket emit error:', error);
      throw error;
    }
  }, []);

  return {
    isConnected,
    connectionError,
    metrics,
    connect,
    disconnect,
    emit
  };
};

/**
 * Hook for subscribing to socket events
 */
export const useSocketEvent = (eventName, callback, dependencies = []) => {
  const callbackRef = useRef(callback);
  
  // Update callback ref when dependencies change
  useEffect(() => {
    callbackRef.current = callback;
  }, dependencies);

  useEffect(() => {
    const wrappedCallback = (...args) => {
      callbackRef.current(...args);
    };

    const unsubscribe = socketService.on(eventName, wrappedCallback);
    
    return unsubscribe;
  }, [eventName]);
};

/**
 * Hook for room management
 */
export const useSocketRoom = (roomId) => {
  const [isJoined, setIsJoined] = useState(false);
  const [members, setMembers] = useState([]);
  const [error, setError] = useState(null);

  const joinRoom = useCallback(async () => {
    try {
      setError(null);
      await socketService.joinRoom(roomId);
      setIsJoined(true);
    } catch (err) {
      setError(err.message);
      setIsJoined(false);
    }
  }, [roomId]);

  const leaveRoom = useCallback(async () => {
    try {
      setError(null);
      await socketService.leaveRoom(roomId);
      setIsJoined(false);
      setMembers([]);
    } catch (err) {
      setError(err.message);
    }
  }, [roomId]);

  // Listen for room events
  useSocketEvent('room:joined', (data) => {
    if (data.roomId === roomId) {
      setIsJoined(true);
      setMembers(data.members || []);
    }
  }, [roomId]);

  useSocketEvent('room:left', (data) => {
    if (data.roomId === roomId) {
      setIsJoined(false);
      setMembers([]);
    }
  }, [roomId]);

  useSocketEvent('room:member_joined', (data) => {
    if (data.roomId === roomId) {
      setMembers(prev => [...prev, data.member]);
    }
  }, [roomId]);

  useSocketEvent('room:member_left', (data) => {
    if (data.roomId === roomId) {
      setMembers(prev => prev.filter(member => member.id !== data.memberId));
    }
  }, [roomId]);

  useSocketEvent('room:members_updated', (data) => {
    if (data.roomId === roomId) {
      setMembers(data.members || []);
    }
  }, [roomId]);

  return {
    isJoined,
    members,
    error,
    joinRoom,
    leaveRoom
  };
};

/**
 * Hook for real-time chat functionality
 */
export const useSocketChat = (roomId) => {
  const [messages, setMessages] = useState([]);
  const [typing, setTyping] = useState([]);
  const { isJoined, joinRoom, leaveRoom } = useSocketRoom(roomId);
  const { emit } = useSocket();
  const typingTimeoutRef = useRef(null);

  // Listen for chat events
  useSocketEvent('chat:message', (data) => {
    if (data.roomId === roomId) {
      setMessages(prev => [...prev, data.message]);
    }
  }, [roomId]);

  useSocketEvent('chat:typing_start', (data) => {
    if (data.roomId === roomId) {
      setTyping(prev => {
        if (!prev.find(user => user.id === data.user.id)) {
          return [...prev, data.user];
        }
        return prev;
      });
    }
  }, [roomId]);

  useSocketEvent('chat:typing_stop', (data) => {
    if (data.roomId === roomId) {
      setTyping(prev => prev.filter(user => user.id !== data.user.id));
    }
  }, [roomId]);

  useSocketEvent('chat:message_deleted', (data) => {
    if (data.roomId === roomId) {
      setMessages(prev => prev.filter(msg => msg.id !== data.messageId));
    }
  }, [roomId]);

  useSocketEvent('chat:message_edited', (data) => {
    if (data.roomId === roomId) {
      setMessages(prev => prev.map(msg => 
        msg.id === data.messageId ? { ...msg, ...data.updates } : msg
      ));
    }
  }, [roomId]);

  const sendMessage = useCallback(async (content, type = 'text') => {
    if (!isJoined) {
      throw new Error('Not joined to room');
    }

    return emit('chat:send_message', {
      roomId,
      content,
      type,
      timestamp: Date.now()
    });
  }, [roomId, isJoined, emit]);

  const startTyping = useCallback(async () => {
    if (!isJoined) return;

    emit('chat:typing_start', { roomId });
    
    // Auto-stop typing after 3 seconds
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, [roomId, isJoined, emit]);

  const stopTyping = useCallback(async () => {
    if (!isJoined) return;

    emit('chat:typing_stop', { roomId });
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, [roomId, isJoined, emit]);

  const deleteMessage = useCallback(async (messageId) => {
    if (!isJoined) return;

    return emit('chat:delete_message', {
      roomId,
      messageId
    });
  }, [roomId, isJoined, emit]);

  const editMessage = useCallback(async (messageId, newContent) => {
    if (!isJoined) return;

    return emit('chat:edit_message', {
      roomId,
      messageId,
      content: newContent
    });
  }, [roomId, isJoined, emit]);

  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    messages,
    typing,
    isJoined,
    joinRoom,
    leaveRoom,
    sendMessage,
    startTyping,
    stopTyping,
    deleteMessage,
    editMessage
  };
};

/**
 * Hook for real-time notifications
 */
export const useSocketNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  useSocketEvent('notification:new', (notification) => {
    setNotifications(prev => [notification, ...prev]);
    setUnreadCount(prev => prev + 1);
  });

  useSocketEvent('notification:read', (data) => {
    setNotifications(prev => prev.map(notif => 
      notif.id === data.notificationId 
        ? { ...notif, read: true }
        : notif
    ));
    setUnreadCount(prev => Math.max(0, prev - 1));
  });

  useSocketEvent('notification:deleted', (data) => {
    setNotifications(prev => prev.filter(notif => notif.id !== data.notificationId));
  });

  const markAsRead = useCallback(async (notificationId) => {
    const { emit } = useSocket();
    return emit('notification:mark_read', { notificationId });
  }, []);

  const markAllAsRead = useCallback(async () => {
    const { emit } = useSocket();
    const result = await emit('notification:mark_all_read');
    setUnreadCount(0);
    setNotifications(prev => prev.map(notif => ({ ...notif, read: true })));
    return result;
  }, []);

  const deleteNotification = useCallback(async (notificationId) => {
    const { emit } = useSocket();
    return emit('notification:delete', { notificationId });
  }, []);

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification
  };
};

export default {
  useSocket,
  useSocketEvent,
  useSocketRoom,
  useSocketChat,
  useSocketNotifications
};
