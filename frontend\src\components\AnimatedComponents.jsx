import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, F<PERSON>, Slide, <PERSON>row, Zoom } from '@mui/material';
import { keyframes } from '@mui/system';

// CSS-in-JS animations
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const slideInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const scaleIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

const bounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
`;

// Animated Box component
export const AnimatedBox = ({ 
  children, 
  animation = 'fadeInUp', 
  duration = 0.6, 
  delay = 0,
  ...props 
}) => {
  const animationMap = {
    fadeInUp,
    slideInLeft,
    slideInRight,
    scaleIn,
    bounce,
    pulse
  };

  return (
    <Box
      sx={{
        animation: `${animationMap[animation]} ${duration}s ease-out ${delay}s both`,
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// Animated Card component
export const AnimatedCard = ({ 
  children, 
  elevation = 2,
  hover = true,
  ...props 
}) => {
  return (
    <Card
      elevation={elevation}
      sx={{
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: hover ? 'pointer' : 'default',
        '&:hover': hover ? {
          elevation: elevation + 4,
          transform: 'translateY(-4px)',
          boxShadow: (theme) => theme.shadows[elevation + 4],
        } : {},
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Card>
  );
};

// Animated Button component
export const AnimatedButton = ({ 
  children, 
  variant = 'contained',
  color = 'primary',
  ripple = true,
  ...props 
}) => {
  return (
    <Button
      variant={variant}
      color={color}
      sx={{
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: (theme) => theme.shadows[8],
        },
        '&:active': {
          transform: 'translateY(0)',
        },
        '&::before': ripple ? {
          content: '""',
          position: 'absolute',
          top: 0,
          left: '-100%',
          width: '100%',
          height: '100%',
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
          transition: 'left 0.5s',
        } : {},
        '&:hover::before': ripple ? {
          left: '100%',
        } : {},
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

// Stagger animation container
export const StaggerContainer = ({ children, staggerDelay = 0.1 }) => {
  return (
    <Box>
      {React.Children.map(children, (child, index) => (
        <AnimatedBox
          key={index}
          animation="fadeInUp"
          delay={index * staggerDelay}
          duration={0.6}
        >
          {child}
        </AnimatedBox>
      ))}
    </Box>
  );
};

// Loading shimmer effect
export const ShimmerBox = ({ width = '100%', height = 20, ...props }) => {
  return (
    <Box
      sx={{
        width,
        height,
        background: 'linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%)',
        backgroundSize: '800px 104px',
        animation: `${shimmer} 1.6s linear infinite`,
        borderRadius: 1,
        ...props.sx
      }}
      {...props}
    />
  );
};

// Page transition wrapper
export const PageTransition = ({ children, direction = 'up' }) => {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const getTransitionProps = () => {
    switch (direction) {
      case 'left':
        return { direction: 'right', timeout: 300 };
      case 'right':
        return { direction: 'left', timeout: 300 };
      case 'down':
        return { direction: 'up', timeout: 300 };
      default:
        return { direction: 'up', timeout: 300 };
    }
  };

  return (
    <Slide in={mounted} {...getTransitionProps()}>
      <Box>
        {children}
      </Box>
    </Slide>
  );
};

// Floating action animation
export const FloatingBox = ({ children, ...props }) => {
  return (
    <Box
      sx={{
        animation: `${pulse} 2s ease-in-out infinite`,
        '&:hover': {
          animation: 'none',
          transform: 'scale(1.05)',
        },
        transition: 'transform 0.3s ease',
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// Reveal animation on scroll
export const RevealOnScroll = ({ children, threshold = 0.1 }) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef();

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return (
    <Box ref={ref}>
      <Fade in={isVisible} timeout={800}>
        <Box>
          {children}
        </Box>
      </Fade>
    </Box>
  );
};

export default {
  AnimatedBox,
  AnimatedCard,
  AnimatedButton,
  StaggerContainer,
  ShimmerBox,
  PageTransition,
  FloatingBox,
  RevealOnScroll
};
