import {
  Box,
  Typo<PERSON>,
  TextField,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Chip,
  IconButton,
  Divider,
  Container
} from '@mui/material';
import {
  Send as SendIcon,
  Summarize as SummarizeIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { getMessages, sendMessage, summarizeThread } from '../api';
import { io } from 'socket.io-client';
import { useToast as useCustomToast } from '../components/ToastProvider';
import LoadingAnimation from '../components/LoadingAnimation';
import { useSocketChat } from '../hooks/useSocket';

// Create socket connection with error handling
const createSocket = () => {
  const socketUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:3001';
  return io(socketUrl, {
    autoConnect: false,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
  });
};

export default function Chat() {
  const { id } = useParams();
  const [msg, setMsg] = useState('');
  const [summary, setSummary] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useCustomToast();
  const bottomRef = useRef(null);

  // Use enhanced socket chat hook
  const {
    messages,
    typing,
    isJoined,
    joinRoom,
    leaveRoom,
    sendMessage,
    startTyping,
    stopTyping
  } = useSocketChat(id);

  useEffect(() => {
    if (!id) return;

    // Initialize socket
    socketRef.current = createSocket();
    const socket = socketRef.current;

    // Socket event handlers
    socket.on('connect', () => {
      setSocketConnected(true);
      socket.emit('joinGroup', id);
    });

    socket.on('disconnect', () => {
      setSocketConnected(false);
    });

    socket.on('newMessage', (message) => {
      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    });

    socket.on('userTyping', ({ userId, isTyping }) => {
      setIsTyping(isTyping);
    });

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      toast({
        title: 'Lỗi kết nối',
        description: 'Không thể kết nối đến server chat',
        status: 'error'
      });
    });

    // Connect socket
    socket.connect();

    // Fetch initial messages
    fetchMessages();

    return () => {
      if (socket) {
        socket.emit('leaveGroup', id);
        socket.disconnect();
      }
    };
    // eslint-disable-next-line
  }, [id]);

  const fetchMessages = async () => {
    try {
      const res = await getMessages(id);
      setMessages(res.data);
    } catch {
      setMessages([]);
    }
  };

  useEffect(() => { bottomRef.current?.scrollIntoView({ behavior: 'smooth' }); }, [messages]);

  const handleSend = async () => {
    if (!msg.trim() || !socketConnected) return;

    try {
      const res = await sendMessage(id, { content: msg.trim() });
      if (socketRef.current) {
        socketRef.current.emit('sendMessage', { groupId: id, message: res.data });
      }
      setMsg('');
    } catch (error) {
      console.error('Send message error:', error);
      toast({
        title: 'Gửi tin nhắn thất bại',
        description: 'Vui lòng thử lại sau',
        status: 'error'
      });
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  const handleSummarize = async () => {
    setLoading(true);
    try {
      const res = await summarizeThread(id);
      setSummary(res.data.summary);
      toast({ title: 'Tóm tắt thành công', status: 'success' });
    } catch {
      toast({ title: 'Tóm tắt thất bại', status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={2} sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Chat nhóm
          </Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <Chip
              label={socketConnected ? 'Đã kết nối' : 'Mất kết nối'}
              color={socketConnected ? 'success' : 'error'}
              size="small"
            />
            <Button
              variant="outlined"
              startIcon={<SummarizeIcon />}
              onClick={handleSummarize}
              disabled={loading || messages.length === 0}
            >
              {loading ? 'Đang tóm tắt...' : 'Tóm tắt AI'}
            </Button>
          </Box>
        </Box>

        {/* AI Summary */}
        {summary && (
          <Paper
            elevation={1}
            sx={{
              p: 2,
              mb: 3,
              backgroundColor: 'primary.50',
              border: '1px solid',
              borderColor: 'primary.200'
            }}
          >
            <Typography variant="body2" color="primary.main">
              <strong>Tóm tắt AI:</strong> {summary}
            </Typography>
          </Paper>
        )}

        {/* Messages */}
        <Paper
          variant="outlined"
          sx={{
            height: 400,
            overflow: 'auto',
            mb: 2,
            p: 1
          }}
        >
          {messages.length === 0 ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              height="100%"
            >
              <Typography variant="body2" color="text.secondary">
                Chưa có tin nhắn nào. Hãy bắt đầu cuộc trò chuyện!
              </Typography>
            </Box>
          ) : (
            <List>
              {messages.map((message, index) => (
                <ListItem key={index} sx={{ py: 1 }}>
                  <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                    <PersonIcon />
                  </Avatar>
                  <ListItemText
                    primary={message.sender?.name || 'Ẩn danh'}
                    secondary={message.content}
                    primaryTypographyProps={{
                      variant: 'subtitle2',
                      color: 'primary.main'
                    }}
                    secondaryTypographyProps={{
                      variant: 'body2',
                      sx: { mt: 0.5 }
                    }}
                  />
                </ListItem>
              ))}
              {isTyping && (
                <ListItem>
                  <Avatar sx={{ mr: 2, bgcolor: 'grey.400' }}>
                    <PersonIcon />
                  </Avatar>
                  <ListItemText
                    primary="Ai đó"
                    secondary={<LoadingAnimation variant="typing" />}
                  />
                </ListItem>
              )}
              <div ref={bottomRef} />
            </List>
          )}
        </Paper>

        {/* Input */}
        <Box display="flex" gap={1}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            value={msg}
            onChange={(e) => setMsg(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Nhập tin nhắn... (Enter để gửi, Shift+Enter để xuống dòng)"
            disabled={!socketConnected}
            variant="outlined"
            size="small"
          />
          <IconButton
            color="primary"
            onClick={handleSend}
            disabled={!msg.trim() || !socketConnected}
            sx={{
              bgcolor: 'primary.main',
              color: 'white',
              '&:hover': {
                bgcolor: 'primary.dark'
              },
              '&:disabled': {
                bgcolor: 'grey.300'
              }
            }}
          >
            <SendIcon />
          </IconButton>
        </Box>
      </Paper>
    </Container>
  );
}