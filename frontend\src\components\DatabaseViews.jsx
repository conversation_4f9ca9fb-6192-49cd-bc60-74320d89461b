import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Button,
  Stack,
  Paper,
  Card,
  CardContent,
  CardMedia,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  IconButton,
  ToggleButton,
  ToggleButtonGroup,
  Grid,
  LinearProgress,
  Badge,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  ViewList as TableIcon,
  ViewKanban as KanbanIcon,
  CalendarToday as CalendarIcon,
  ViewModule as GalleryIcon,
  Timeline as TimelineIcon,
  Add as AddIcon,
  MoreVert as MoreIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';

const DatabaseViews = ({ data = [], viewType = 'table', onViewChange, onItemClick, onItemUpdate }) => {
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);

  // Group data for Kanban view
  const kanbanColumns = useMemo(() => {
    const columns = {
      'not-started': { title: '📋 Not Started', items: [], color: 'default' },
      'in-progress': { title: '🔄 In Progress', items: [], color: 'primary' },
      'review': { title: '👀 Review', items: [], color: 'warning' },
      'completed': { title: '✅ Completed', items: [], color: 'success' }
    };

    data.forEach(item => {
      const status = item.status || 'not-started';
      if (columns[status]) {
        columns[status].items.push(item);
      }
    });

    return columns;
  }, [data]);

  // Group data by date for Calendar view
  const calendarData = useMemo(() => {
    const grouped = {};
    data.forEach(item => {
      if (item.deadline) {
        const date = new Date(item.deadline).toDateString();
        if (!grouped[date]) grouped[date] = [];
        grouped[date].push(item);
      }
    });
    return grouped;
  }, [data]);

  const renderTableView = () => (
    <TableContainer component={Paper} elevation={2}>
      <Table>
        <TableHead>
          <TableRow sx={{ bgcolor: 'grey.50' }}>
            <TableCell><strong>Title</strong></TableCell>
            <TableCell><strong>Status</strong></TableCell>
            <TableCell><strong>Priority</strong></TableCell>
            <TableCell><strong>Assignee</strong></TableCell>
            <TableCell><strong>Due Date</strong></TableCell>
            <TableCell><strong>Progress</strong></TableCell>
            <TableCell width={50}></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((item, index) => (
            <TableRow 
              key={item.id || index}
              hover
              onClick={() => onItemClick?.(item)}
              sx={{ cursor: 'pointer' }}
            >
              <TableCell>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <DragIcon color="action" fontSize="small" />
                  <Typography variant="body2" fontWeight="medium">
                    {item.title}
                  </Typography>
                </Stack>
              </TableCell>
              <TableCell>
                <Chip
                  label={item.status || 'Not Started'}
                  size="small"
                  color={
                    item.status === 'completed' ? 'success' :
                    item.status === 'in-progress' ? 'primary' :
                    item.status === 'review' ? 'warning' : 'default'
                  }
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={item.priority || 'Medium'}
                  size="small"
                  variant="outlined"
                  color={
                    item.priority === 'urgent' ? 'error' :
                    item.priority === 'high' ? 'warning' :
                    item.priority === 'low' ? 'success' : 'info'
                  }
                />
              </TableCell>
              <TableCell>
                <Avatar sx={{ width: 24, height: 24 }}>
                  {item.assignee?.charAt(0) || 'U'}
                </Avatar>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="text.secondary">
                  {item.deadline ? new Date(item.deadline).toLocaleDateString() : '-'}
                </Typography>
              </TableCell>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={item.progress || 0}
                    sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="caption">
                    {item.progress || 0}%
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedItem(item);
                    setMenuAnchor(e.currentTarget);
                  }}
                >
                  <MoreIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderKanbanView = () => (
    <Grid container spacing={2}>
      {Object.entries(kanbanColumns).map(([key, column]) => (
        <Grid item xs={12} md={3} key={key}>
          <Paper elevation={1} sx={{ p: 2, height: 'fit-content', minHeight: 400 }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" fontWeight="bold">
                {column.title}
              </Typography>
              <Badge badgeContent={column.items.length} color={column.color}>
                <IconButton size="small">
                  <AddIcon />
                </IconButton>
              </Badge>
            </Stack>
            
            <Stack spacing={2}>
              {column.items.map((item, index) => (
                <Card
                  key={item.id || index}
                  elevation={2}
                  onClick={() => onItemClick?.(item)}
                  sx={{
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': { transform: 'translateY(-2px)' }
                  }}
                >
                  <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                    <Typography variant="body2" fontWeight="medium" gutterBottom>
                      {item.title}
                    </Typography>
                    
                    <Stack direction="row" spacing={1} sx={{ mb: 1 }}>
                      <Chip
                        label={item.priority || 'Medium'}
                        size="small"
                        variant="outlined"
                        color={
                          item.priority === 'urgent' ? 'error' :
                          item.priority === 'high' ? 'warning' : 'default'
                        }
                      />
                    </Stack>
                    
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Avatar sx={{ width: 20, height: 20, fontSize: '0.75rem' }}>
                        {item.assignee?.charAt(0) || 'U'}
                      </Avatar>
                      <Typography variant="caption" color="text.secondary">
                        {item.deadline ? new Date(item.deadline).toLocaleDateString() : ''}
                      </Typography>
                    </Stack>
                    
                    {item.progress > 0 && (
                      <LinearProgress
                        variant="determinate"
                        value={item.progress}
                        sx={{ mt: 1, height: 4, borderRadius: 2 }}
                      />
                    )}
                  </CardContent>
                </Card>
              ))}
            </Stack>
          </Paper>
        </Grid>
      ))}
    </Grid>
  );

  const renderCalendarView = () => {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const firstDay = new Date(currentYear, currentMonth, 1).getDay();

    const days = [];
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }

    return (
      <Paper elevation={2} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          {today.toLocaleDateString('vi-VN', { month: 'long', year: 'numeric' })}
        </Typography>
        
        <Grid container spacing={1}>
          {['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'].map(day => (
            <Grid item xs key={day}>
              <Typography variant="caption" fontWeight="bold" sx={{ p: 1, display: 'block', textAlign: 'center' }}>
                {day}
              </Typography>
            </Grid>
          ))}
          
          {days.map((day, index) => {
            const dateStr = day ? new Date(currentYear, currentMonth, day).toDateString() : null;
            const dayItems = dateStr ? calendarData[dateStr] || [] : [];
            
            return (
              <Grid item xs key={index}>
                <Paper
                  variant="outlined"
                  sx={{
                    minHeight: 80,
                    p: 0.5,
                    bgcolor: day === today.getDate() ? 'primary.50' : 'transparent'
                  }}
                >
                  {day && (
                    <>
                      <Typography variant="caption" sx={{ fontWeight: day === today.getDate() ? 'bold' : 'normal' }}>
                        {day}
                      </Typography>
                      <Stack spacing={0.5} sx={{ mt: 0.5 }}>
                        {dayItems.slice(0, 2).map((item, i) => (
                          <Chip
                            key={i}
                            label={item.title}
                            size="small"
                            sx={{ fontSize: '0.6rem', height: 16 }}
                            onClick={() => onItemClick?.(item)}
                          />
                        ))}
                        {dayItems.length > 2 && (
                          <Typography variant="caption" color="text.secondary">
                            +{dayItems.length - 2} more
                          </Typography>
                        )}
                      </Stack>
                    </>
                  )}
                </Paper>
              </Grid>
            );
          })}
        </Grid>
      </Paper>
    );
  };

  const renderGalleryView = () => (
    <Grid container spacing={2}>
      {data.map((item, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={item.id || index}>
          <Card
            elevation={2}
            onClick={() => onItemClick?.(item)}
            sx={{
              cursor: 'pointer',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'translateY(-4px)' }
            }}
          >
            <CardMedia
              sx={{
                height: 140,
                bgcolor: 'grey.200',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography variant="h4">
                {item.emoji || '📄'}
              </Typography>
            </CardMedia>
            <CardContent>
              <Typography variant="h6" gutterBottom noWrap>
                {item.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {item.description || 'No description'}
              </Typography>
              <Stack direction="row" spacing={1}>
                <Chip
                  label={item.status || 'Not Started'}
                  size="small"
                  color={item.status === 'completed' ? 'success' : 'default'}
                />
                <Chip
                  label={item.priority || 'Medium'}
                  size="small"
                  variant="outlined"
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderTimelineView = () => (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        📊 Project Timeline
      </Typography>
      
      <Stack spacing={3}>
        {data
          .filter(item => item.deadline)
          .sort((a, b) => new Date(a.deadline) - new Date(b.deadline))
          .map((item, index) => (
            <Box key={item.id || index} sx={{ position: 'relative', pl: 4 }}>
              {/* Timeline line */}
              <Box
                sx={{
                  position: 'absolute',
                  left: 8,
                  top: 0,
                  bottom: index === data.length - 1 ? '50%' : 0,
                  width: 2,
                  bgcolor: 'primary.main'
                }}
              />
              
              {/* Timeline dot */}
              <Box
                sx={{
                  position: 'absolute',
                  left: 4,
                  top: 8,
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  bgcolor: item.status === 'completed' ? 'success.main' : 'primary.main'
                }}
              />
              
              <Card elevation={1} onClick={() => onItemClick?.(item)} sx={{ cursor: 'pointer' }}>
                <CardContent sx={{ p: 2 }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="subtitle2" fontWeight="bold">
                      {item.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(item.deadline).toLocaleDateString()}
                    </Typography>
                  </Stack>
                  
                  <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                    <Chip label={item.status || 'Not Started'} size="small" />
                    <Chip label={item.priority || 'Medium'} size="small" variant="outlined" />
                  </Stack>
                  
                  {item.progress > 0 && (
                    <LinearProgress
                      variant="determinate"
                      value={item.progress}
                      sx={{ mt: 1, height: 4, borderRadius: 2 }}
                    />
                  )}
                </CardContent>
              </Card>
            </Box>
          ))}
      </Stack>
    </Paper>
  );

  const renderView = () => {
    switch (viewType) {
      case 'kanban': return renderKanbanView();
      case 'calendar': return renderCalendarView();
      case 'gallery': return renderGalleryView();
      case 'timeline': return renderTimelineView();
      default: return renderTableView();
    }
  };

  return (
    <Box>
      {/* View Selector */}
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <ToggleButtonGroup
          value={viewType}
          exclusive
          onChange={(e, value) => value && onViewChange?.(value)}
          size="small"
        >
          <ToggleButton value="table">
            <Tooltip title="Table View">
              <TableIcon />
            </Tooltip>
          </ToggleButton>
          <ToggleButton value="kanban">
            <Tooltip title="Kanban Board">
              <KanbanIcon />
            </Tooltip>
          </ToggleButton>
          <ToggleButton value="calendar">
            <Tooltip title="Calendar View">
              <CalendarIcon />
            </Tooltip>
          </ToggleButton>
          <ToggleButton value="gallery">
            <Tooltip title="Gallery View">
              <GalleryIcon />
            </Tooltip>
          </ToggleButton>
          <ToggleButton value="timeline">
            <Tooltip title="Timeline View">
              <TimelineIcon />
            </Tooltip>
          </ToggleButton>
        </ToggleButtonGroup>

        <Button
          startIcon={<AddIcon />}
          variant="contained"
          size="small"
          sx={{ borderRadius: 2 }}
        >
          Add Item
        </Button>
      </Stack>

      {/* View Content */}
      {renderView()}

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => onItemClick?.(selectedItem)}>
          Edit
        </MenuItem>
        <MenuItem onClick={() => {}}>
          Duplicate
        </MenuItem>
        <MenuItem onClick={() => {}}>
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DatabaseViews;
