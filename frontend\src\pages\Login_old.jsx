import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Stack
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import { login as apiLogin } from '../api';
import { useAuth } from '../App';
import { useToast } from '../components/ToastProvider';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();
  const location = useLocation();

  // Nhận token từ Google OAuth callback
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const token = params.get('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        login({ name: payload.name || 'User', ...payload }, token);
        toast({ title: '<PERSON><PERSON><PERSON> nhập Google thành công', status: 'success' });
        navigate('/');
      } catch {}
    }
  }, [location.search]);

  const handleLogin = async () => {
    setLoading(true);
    try {
      const res = await apiLogin(email, password);
      const token = res.data.token;
      let userObj = res.data.user;
      if (!userObj && token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          userObj = { name: payload.name || 'User', ...payload };
        } catch { userObj = { name: 'User' }; }
      }
      login(userObj, token);
      toast({ title: 'Đăng nhập thành công', status: 'success' });
      navigate('/');
    } catch (err) {
      toast({ title: 'Đăng nhập thất bại', description: err.response?.data?.message, status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleGoogle = () => {
    window.location.href = 'http://localhost:3001/api/auth/google';
  };

  return (
    <Box maxW="sm" mx="auto" sx={{ mt: 16 }} sx={{ p: 8 }} borderWidth={1} borderRadius="lg" boxShadow="lg">
      <Typography variant="h4" component="h1" sx={{ mb: 6 }} textAlign="center">Đăng nhập</Typography>
      <Stack spacing={4}>
        <TextField variant="outlined" placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
        <TextField variant="outlined" placeholder="Mật khẩu" type="password" value={password} onChange={e => setPassword(e.target.value)} />
        <Button color="primary" disabled={loading} onClick={handleLogin}>Đăng nhập</Button>
        <Button color="primary" variant="outline" onClick={handleGoogle}>Đăng nhập với Google</Button>
      </Stack>
      <Typography variant="body1" sx={{ mt: 4 }} textAlign="center">Chưa có tài khoản? <a href="/register" style={{color: '#3182ce'}}>Đăng ký</a></Typography>
    </Box>
  );
} 