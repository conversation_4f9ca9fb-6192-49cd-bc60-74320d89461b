import { useState, useEffect } from 'react';
import {
 Box, Button, Input, Heading, Stack, Text 
} from '../components/ChakraToMui';
import { useNavigate, useLocation } from 'react-router-dom';
import { login as apiLogin } from '../api';
import { useAuth } from '../App';
import { useToast } from '../components/ToastProvider';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();
  const location = useLocation();

  // Nhận token từ Google OAuth callback
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const token = params.get('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        login({ name: payload.name || 'User', ...payload }, token);
        toast({ title: '<PERSON><PERSON>ng nhập Google thành công', status: 'success' });
        navigate('/');
      } catch {}
    }
  }, [location.search]);

  const handleLogin = async () => {
    setLoading(true);
    try {
      const res = await apiLogin(email, password);
      const token = res.data.token;
      let userObj = res.data.user;
      if (!userObj && token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          userObj = { name: payload.name || 'User', ...payload };
        } catch { userObj = { name: 'User' }; }
      }
      login(userObj, token);
      toast({ title: 'Đăng nhập thành công', status: 'success' });
      navigate('/');
    } catch (err) {
      toast({ title: 'Đăng nhập thất bại', description: err.response?.data?.message, status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleGoogle = () => {
    window.location.href = 'http://localhost:3001/api/auth/google';
  };

  return (
    <Box maxW="sm" mx="auto" mt={16} p={8} borderWidth={1} borderRadius="lg" boxShadow="lg">
      <Heading mb={6} textAlign="center">Đăng nhập</Heading>
      <Stack spacing={4}>
        <Input placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
        <Input placeholder="Mật khẩu" type="password" value={password} onChange={e => setPassword(e.target.value)} />
        <Button colorScheme="teal" isLoading={loading} onClick={handleLogin}>Đăng nhập</Button>
        <Button colorScheme="red" variant="outline" onClick={handleGoogle}>Đăng nhập với Google</Button>
      </Stack>
      <Text mt={4} textAlign="center">Chưa có tài khoản? <a href="/register" style={{color: '#3182ce'}}>Đăng ký</a></Text>
    </Box>
  );
} 