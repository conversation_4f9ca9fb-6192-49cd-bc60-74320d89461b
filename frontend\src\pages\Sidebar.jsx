import React from 'react';
import {
  Box,
  Stack,
  Link as ChakraLink
} from '@mui/material';
import { Link, useLocation } from 'react-router-dom';

const navs = [
  { to: '/', label: 'Trang chủ' },
  { to: '/todo', label: 'To-Do' },
  { to: '/group', label: 'Nhóm' },
  { to: '/forum', label: '<PERSON>ễn đàn' },
  { to: '/event', label: 'Sự kiện' },
  { to: '/exam', label: 'Bài kiểm tra' },
  { to: '/resource', label: 'Tài nguyên' },
  { to: '/mentor', label: '<PERSON><PERSON>' },
  { to: '/feedback', label: 'Feedback' },
  { to: '/progress', label: 'Tiến độ' },
  { to: '/notification', label: 'Thông báo' },
  { to: '/leaderboard', label: 'Bảng xếp hạng' },
  { to: '/calendar', label: '<PERSON>ị<PERSON>' },
  { to: '/file', label: 'Tệp' },
  { to: '/chatbot', label: 'AI Chatbot' },
];

export default function Sidebar() {
  const location = useLocation();
  return (
    <Box as="nav" w={{ base: '100%', md: '220px' }} bg="gray.50" sx={{ p: 4 }} minH="100vh" boxShadow="md">
      <Stack direction="column" align="stretch" spacing={2}>
        {navs.map(nav => (
          <ChakraLink
            as={Link}
            to={nav.to}
            key={nav.to}
            fontWeight={location.pathname === nav.to ? 'bold' : 'normal'}
            color={location.pathname === nav.to ? 'teal.500' : 'gray.700'}
            _hover={{ color: 'teal.600' }}
            sx={{ p: 2 }}
            borderRadius="md"
            bg={location.pathname === nav.to ? 'teal.50' : 'transparent'}
          >
            {nav.label}
          </ChakraLink>
        ))}
      </Stack>
    </Box>
  );
} 