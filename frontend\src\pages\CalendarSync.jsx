import { useState } from 'react';
import {
 Box, Heading, Button, Text 
} from '../components/ChakraToMui';
import { useToast } from '../components/ToastProvider';
import { syncCalendar } from '../api';

export default function CalendarSync() {
  const [result, setResult] = useState('');
  const toast = useToast();

  const handleSync = async () => {
    try {
      const res = await syncCalendar({}); // TODO: Truyền access_token, todos thực tế
      setResult(res.data.message);
      toast({ title: 'Đã gửi yêu cầu đồng bộ', status: 'info' });
    } catch {
      setResult('Lỗi đồng bộ');
      toast({ title: 'Lỗi đồng bộ', status: 'error' });
    }
  };

  return (
    <Box>
      <Heading mb={4}>Đồng bộ Google Calendar</Heading>
      <Text mb={4}>T<PERSON>h năng này sẽ đồng bộ to-do/lịch học với <PERSON> Calendar của bạn. (<PERSON><PERSON><PERSON> cấ<PERSON> hì<PERSON>, API key)</Text>
      <Button colorScheme="blue" onClick={handleSync}>Đồng bộ ngay</Button>
      {result && <Text mt={4}>{result}</Text>}
      <Text mt={8} color="gray.500" fontSize="sm">TODO: Cần cấu hình Google OAuth, lấy access_token từ user, dùng googleapis để đồng bộ. Xem hướng dẫn <a href="https://developers.google.com/calendar/api/quickstart/nodejs" target="_blank" rel="noopener noreferrer" style={{color: '#3182ce'}}>tại đây</a>.</Text>
    </Box>
  );
} 