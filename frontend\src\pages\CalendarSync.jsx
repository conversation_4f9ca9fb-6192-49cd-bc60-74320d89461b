import { useState } from 'react';
import {
  Box,
  Typography,
  Button
} from '@mui/material';
import { useToast } from '../components/ToastProvider';
import { syncCalendar } from '../api';

export default function CalendarSync() {
  const [result, setResult] = useState('');
  const toast = useToast();

  const handleSync = async () => {
    try {
      const res = await syncCalendar({}); // TODO: Truyền access_token, todos thực tế
      setResult(res.data.message);
      toast({ title: 'Đ<PERSON> gửi yêu cầu đồng bộ', status: 'info' });
    } catch {
      setResult('Lỗi đồng bộ');
      toast({ title: 'Lỗi đồng bộ', status: 'error' });
    }
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" sx={{ mb: 4 }}>Đồng bộ Google Calendar</Typography>
      <Typography variant="body1" sx={{ mb: 4 }}>T<PERSON><PERSON> năng này sẽ đồng bộ to-do/lịch h<PERSON> vớ<PERSON> Calendar của bạn. (<PERSON><PERSON>n c<PERSON><PERSON>, API key)</Typography>
      <Button color="primary" onClick={handleSync}>Đồng bộ ngay</Button>
      {result && <Typography variant="body1" sx={{ mt: 4 }}>{result}</Typography>}
      <Typography variant="body1" sx={{ mt: 8 }} color="gray.500" fontSize="sm">TODO: Cần cấu hình Google OAuth, lấy access_token từ user, dùng googleapis để đồng bộ. Xem hướng dẫn <a href="https://developers.google.com/calendar/api/quickstart/nodejs" target="_blank" rel="noopener noreferrer" style={{color: '#3182ce'}}>tại đây</a>.</Typography>
    </Box>
  );
} 