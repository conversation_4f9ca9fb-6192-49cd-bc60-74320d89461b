import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Stack,
  Button,
  TextField,
  Link
} from '@mui/material';
import { uploadFile, getFileList } from '../api';
import { useToast } from '../components/ToastProvider';

export default function FileManager() {
  const [file, setFile] = useState(null);
  const [files, setFiles] = useState([]);
  const toast = useToast();

  const fetchFiles = async () => {
    const res = await getFileList();
    setFiles(res.data);
  };
  useEffect(() => { fetchFiles(); }, []);

  const handleUpload = async () => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await uploadFile(formData);
    setFile(null);
    fetchFiles();
    toast({ title: 'Đã upload file', status: 'success' });
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" sx={{ mb: 4 }}><PERSON><PERSON><PERSON>n lý tài liệu</Typography>
      <Stack direction="row" sx={{ mb: 4 }}>
        <TextField variant="outlined" type="file" onChange={e => setFile(e.target.files[0])} />
        <Button color="primary" onClick={handleUpload}>Upload</Button>
      </Stack>
      <Stack spacing={3}>
        {files.map(f => (
          <Box key={f.filename} sx={{ p: 3 }} borderWidth={1} borderRadius="md" display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="body1">{f.filename}</Typography>
            <Link href={f.url} isExternal color="blue.500"><Button size="sm">Tải về</Button></Link>
          </Box>
        ))}
      </Stack>
    </Box>
  );
} 