import { useState, useEffect } from 'react';
import {
 Box, Heading, Stack, Button, Input, Text, Link 
} from '../components/ChakraToMui';
import { uploadFile, getFileList } from '../api';
import { useToast } from '../components/ToastProvider';

export default function FileManager() {
  const [file, setFile] = useState(null);
  const [files, setFiles] = useState([]);
  const toast = useToast();

  const fetchFiles = async () => {
    const res = await getFileList();
    setFiles(res.data);
  };
  useEffect(() => { fetchFiles(); }, []);

  const handleUpload = async () => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await uploadFile(formData);
    setFile(null);
    fetchFiles();
    toast({ title: 'Đã upload file', status: 'success' });
  };

  return (
    <Box>
      <Heading mb={4}><PERSON><PERSON><PERSON><PERSON> lý tài liệu</Heading>
      <Stack direction="row" mb={4}>
        <Input type="file" onChange={e => setFile(e.target.files[0])} />
        <Button colorScheme="teal" onClick={handleUpload}>Upload</Button>
      </Stack>
      <Stack spacing={3}>
        {files.map(f => (
          <Box key={f.filename} p={3} borderWidth={1} borderRadius="md" display="flex" alignItems="center" justifyContent="space-between">
            <Text>{f.filename}</Text>
            <Link href={f.url} isExternal color="blue.500"><Button size="sm">Tải về</Button></Link>
          </Box>
        ))}
      </Stack>
    </Box>
  );
} 